import jsPDF from "jspdf";
import { FontInfo, RenderTextOptions } from "../types";

/**
 * Load logo from URL and return base64 string
 */
export const loadLogo = async (logoUrl: string): Promise<string | null> => {
  try {
    const response = await fetch(logoUrl);
    if (!response.ok) {
      throw new Error(`Failed to fetch logo: ${response.status}`);
    }
    
    const logoData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(logoData);
    
    // Validate PNG header
    if (uint8Array.length < 8 || 
        uint8Array[0] !== 0x89 || uint8Array[1] !== 0x50 || 
        uint8Array[2] !== 0x4E || uint8Array[3] !== 0x47) {
      throw new Error("Invalid PNG file format");
    }
    
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading logo:", error);
    return null;
  }
};

/**
 * Load font and return base64 string
 */
export const loadFont = async (fontUrl: string): Promise<string> => {
  try {
    const response = await fetch(fontUrl);
    const fontData = await response.arrayBuffer();
    const uint8Array = new Uint8Array(fontData);
    let binary = "";
    for (let i = 0; i < uint8Array.length; i++) {
      binary += String.fromCharCode(uint8Array[i]);
    }
    return btoa(binary);
  } catch (error) {
    console.error("Error loading font:", error);
    return "";
  }
};

/**
 * Font loading system for Arabic + English with proper fallback
 */
export const loadPerfectFont = async (doc: jsPDF): Promise<FontInfo> => {
  try {
    // Try to load Noto Sans Arabic first (BEST for Arabic + English mixed content)
    const regularResponse = await fetch('/NotoSansArabic-Regular.ttf');
    if (regularResponse.ok) {
      const fontData = await regularResponse.arrayBuffer();
      const uint8Array = new Uint8Array(fontData);
      let binary = "";
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const fontBase64 = btoa(binary);

      doc.addFileToVFS('NotoSansArabic-Regular.ttf', fontBase64);
      doc.addFont('NotoSansArabic-Regular.ttf', 'NotoSansArabic', 'normal');

      console.log('✅ Noto Sans Arabic font loaded successfully - PERFECT for mixed content!');
      // Don't try to load bold version since it doesn't exist
      return { fontName: 'NotoSansArabic', hasBold: false };
    }
  } catch (error) {
    console.warn('⚠️ Noto Sans Arabic failed, trying Amiri:', error);
  }

  try {
    // Fallback to Amiri font
    const response = await fetch('/Amiri-Regular.ttf');
    if (response.ok) {
      const fontData = await response.arrayBuffer();
      const uint8Array = new Uint8Array(fontData);
      let binary = "";
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i]);
      }
      const fontBase64 = btoa(binary);

      doc.addFileToVFS('Amiri-Regular.ttf', fontBase64);
      doc.addFont('Amiri-Regular.ttf', 'Amiri', 'normal');
      console.log('✅ Amiri font loaded as fallback');
      return { fontName: 'Amiri', hasBold: false };
    }
  } catch (error) {
    console.warn('⚠️ Amiri font also failed, using system fonts:', error);
  }

  // Final fallback to system fonts (these have bold support)
  console.log('✅ Using Helvetica as fallback with bold support');
  return { fontName: 'helvetica', hasBold: true }; // System fonts have bold
};

/**
 * Get font name that works with autoTable - autoTable has limited font support
 */
export const getAutoTableFont = (fontInfo?: FontInfo, language?: string): string => {
  // autoTable only supports these built-in fonts reliably:
  // helvetica, times, courier
  // Custom fonts often don't work properly with autoTable

  console.log('🔤 getAutoTableFont called with:', {
    fontInfoName: fontInfo?.fontName,
    language,
    willUse: 'helvetica'
  });

  // Always use helvetica for autoTable as it's the most reliable
  // and has the best support for mixed content
  return 'helvetica';
};

/**
 * Text rendering for Arabic + English
 */
export const renderText = (
  doc: jsPDF, 
  text: string, 
  x: number, 
  y: number, 
  options: RenderTextOptions = {}
): void => {
  const { align = 'left', language = 'en', fontSize, fontName } = options;

  if (fontSize) {
    doc.setFontSize(fontSize);
  }

  // Use the font that was loaded, with proper fallback
  if (fontName) {
    try {
      doc.setFont(fontName, 'normal');
    } catch (error) {
      console.warn(`Font ${fontName} not available, using helvetica:`, error);
      doc.setFont('helvetica', 'normal');
    }
  } else {
    // Default to helvetica if no font specified
    doc.setFont('helvetica', 'normal');
  }

  // Clean and prepare text - don't over-process it
  let finalText = text || '';

  // Render text with proper alignment
  try {
    doc.text(finalText, x, y, { align: align as any });
  } catch (error) {
    console.warn('Text rendering error:', error);
    // Fallback rendering without alignment
    doc.text(finalText, x, y);
  }
};

/**
 * Get appropriate font for tables - use the best available font
 */
export const getTableFont = (fontInfo?: FontInfo): string => {
  if (fontInfo?.fontName) {
    return fontInfo.fontName;
  }
  return 'helvetica'; // Safe fallback that always works
};

/**
 * Get appropriate font style for headers
 */
export const getHeaderFontStyle = (fontInfo?: FontInfo): string | undefined => {
  return fontInfo?.hasBold ? 'bold' : undefined;
};

/**
 * Fallback font system - use this for all hardcoded font references
 */
export const getFallbackFont = (): string => {
  return 'helvetica'; // Safe fallback that always has bold support
};
