import React, { useState } from "react";
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";
import useLanguage from "@/hooks/useLanguage";

interface ExpectedVsActualSectionProps {
  events: EventDetails[];
}

// Helper functions
const formatNumberBasedOnLanguage = (value: number, language: string) => {
  if (language === "ar") {
    return value.toLocaleString("ar-EG", {
      style: "currency",
      currency: "EGP",
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
    });
  }
  return value.toLocaleString("en-US", {
    style: "currency",
    currency: "EGP",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
  });
};

const toLanguageNumerals = (num: number, language: string) => {
  if (language === "ar") {
    const arabicNumerals = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
    return num
      .toString()
      .replace(/[0-9]/g, (digit) => arabicNumerals[parseInt(digit)]);
  }
  return num.toString();
};

const ExpectedVsActualSection: React.FC<ExpectedVsActualSectionProps> = ({
  events,
}) => {
  const { t, language } = useLanguage();
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [selectedYear, setSelectedYear] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string | null>(null);

  // Formatter to remove decimal points
  const formatNumber = (value: number) => {
    if (value >= 1_000_000) {
      return language === "ar"
        ? `${convertToArabicNumerals(Math.round(value / 1_000_000))}M`
        : `${Math.round(value / 1_000_000)}M`;
    } else if (value >= 1_000) {
      return language === "ar"
        ? `${convertToArabicNumerals(Math.round(value / 1_000))}K`
        : `${Math.round(value / 1_000)}K`;
    } else {
      return language === "ar"
        ? convertToArabicNumerals(Math.round(value))
        : Math.round(value).toString();
    }
  };

  const convertToArabicNumerals = (num: number | string) => {
    const arabicDigits = ["٠", "١", "٢", "٣", "٤", "٥", "٦", "٧", "٨", "٩"];
    return num
      .toString()
      .split("")
      .map((digit) => (/\d/.test(digit) ? arabicDigits[Number(digit)] : digit))
      .join("");
  };

  const toNumerals = (num: number) => toLanguageNumerals(num, language);

  const data = events.reduce(
    (acc, event) => {
      const date = new Date(event.dueDate);
      let amount = event.amount;
      if (event.category !== "income" && amount > 0) {
        amount = -amount;
      }

      const monthYear = date.toLocaleString("default", {
        month: "short",
        year: "numeric",
      });
      const existing = acc.find((item) => item.name === monthYear);
      if (existing) {
        existing.expected +=
          event.status === "upcoming" || event.status === "overdue"
            ? Number(amount)
            : 0;
        existing.actual += event.status === "completed" ? Number(amount) : 0;
      } else {
        acc.push({
          name: monthYear,
          expected:
            event.status === "upcoming" || event.status === "overdue"
              ? Number(amount)
              : 0,
          actual: event.status === "completed" ? Number(amount) : 0,
        });
      }
      return acc;
    },
    [] as { name: string; expected: number; actual: number }[],
  );

  // Get unique months, years, and statuses from data
  const months = Array.from(
    new Set(data.map((item) => item.name.split(" ")[0])),
  );
  const years = Array.from(
    new Set(data.map((item) => item.name.split(" ")[1])),
  );
  const statuses = ["completed", "overdue", "upcoming"];


    const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded border bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800">
          <p className="font-semibold">{label}</p>
          {payload.map((item: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: item.color }}>
              {item.name}: {formatNumber(item.value)}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };


  // Filter data based on selected month, year, and status
  const filteredData = data.filter((item) => {
    
    const [month, year] = item.name.split(" ");
    return (
        <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700" dir={language === 'ar' ? 'rtl' : 'ltr'}>
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold dark:text-white">{t("Expected vs Actual")}</h2>
                <div className="flex gap-4">
                    <select
                        className="p-2 border rounded"
                        value={selectedMonth || ""}
                        onChange={e => setSelectedMonth(e.target.value || null)}
                    >
                        <option value="">{t("All Months")}</option>
                        {months.map(month => (
                            <option key={month} value={month}>
                                {t(month)}
                            </option>
                        ))}
                    </select>
                    <select
                        className="p-2 border rounded"
                        value={selectedYear || ""}
                        onChange={e => setSelectedYear(e.target.value || null)}
                    >
                        <option value="">{t("All Years")}</option>
                        {years.map(year => (
                            <option key={year} value={year}>
                                {toNumerals(parseInt(year))}
                            </option>
                        ))}
                    </select>
                    <select
                        className="p-2 border rounded"
                        value={selectedStatus || ""}
                        onChange={e => setSelectedStatus(e.target.value || null)}
                    >
                        <option value="">{t("All Statuses")}</option>
                        {statuses.map(status => (
                            <option key={status} value={status}>
                                {t(status)}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
            
        </div>
    );
  });

      const formattedData = filteredData.map((item) => ({
    ...item,
    name:
      item.name.split(" ")[0] +
      " " +
      toNumerals(parseInt(item.name.split(" ")[1])),
  }));



  return (
    <div
      className="rounded-lg border bg-white p-3 shadow-sm dark:border-gray-700 dark:bg-gray-800 sm:p-4 lg:p-6"
      dir={language === "ar" ? "rtl" : "ltr"}
    >
      <div className="mb-4 flex flex-col space-y-4 sm:mb-6 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <h2 className="text-lg font-semibold dark:text-white sm:text-xl lg:text-2xl">
          {t("Expected vs Actual")}
        </h2>
        <div className="flex flex-col gap-2 sm:flex-row sm:gap-3 lg:gap-4">
          <select
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            value={selectedMonth || ""}
            onChange={(e) => setSelectedMonth(e.target.value || null)}
          >
            <option value="">{t("All Months")}</option>
            {months.map((month) => (
              <option key={month} value={month}>
                {t(month)}
              </option>
            ))}
          </select>
          <select
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            value={selectedYear || ""}
            onChange={(e) => setSelectedYear(e.target.value || null)}
          >
            <option value="">{t("All Years")}</option>
            {years.map((year) => (
              <option key={year} value={year}>
                {toNumerals(parseInt(year))}
              </option>
            ))}
          </select>
          <select
            className="rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200"
            value={selectedStatus || ""}
            onChange={(e) => setSelectedStatus(e.target.value || null)}
          >
            <option value="">{t("All Statuses")}</option>
            {statuses.map((status) => (
              <option key={status} value={status}>
                {t(status)}
              </option>
            ))}
          </select>
        </div>
      </div>
      <ResponsiveContainer
        width="100%"
        height={300}
        className="sm:h-[350px] lg:h-[400px]"
      >
        <LineChart data={formattedData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis
            tickFormatter={formatNumber}
            tick={{ dx: language === "ar" ? -55 : 0 }}
          />
          <Tooltip
            content={<CustomTooltip />}
            contentStyle={{
              textAlign: language === "ar" ? "right" : "left",
              direction: language === "ar" ? "rtl" : "ltr",
            }}
          />
          <Legend />
          <Line
            type="monotone"
            dataKey="expected"
            stroke="#8884d8"
            name={t("Expected")}
          />
          <Line
            type="monotone"
            dataKey="actual"
            stroke="#82ca9d"
            name={t("Actual")}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default ExpectedVsActualSection;
