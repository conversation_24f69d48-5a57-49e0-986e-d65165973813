import IncomeServices from "./income";
import ExpenseServices from "./expenses";
import { EventDetails } from "./interfaces/finaces";

const getCombinedEvents = async (): Promise<EventDetails[]> => {
  try {
    // Fetch incomes and expenses in parallel
    const [incomes, expenses] = await Promise.all([
      IncomeServices().getIncomes(),
      ExpenseServices().getExpenses(),
    ]);

    // Handle cases where incomes or expenses are null or empty
    const validIncomes = incomes?.length ? incomes : [];
    const validExpenses = expenses?.length ? expenses : [];

    const combinedEvents = [...validIncomes, ...validExpenses];

    // Sort the combined array by dueDate
    combinedEvents.sort(
      (a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()
    );

    return combinedEvents;
  } catch (error) {
    console.error("Error fetching combined events:", error);
    throw new Error("Error fetching combined events");
  }
};

export default getCombinedEvents;