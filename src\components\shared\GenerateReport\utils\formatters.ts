/**
 * Convert numbers to Arabic numerals
 */
export const convertToArabicNumerals = (num: number | string | undefined | null): string => {
  if (num === undefined || num === null) return "";
  const numStr = Math.floor(Number(num)).toString();
  return numStr.replace(/\d/g, (d) => "٠١٢٣٤٥٦٧٨٩"[parseInt(d)]);
};

/**
 * Format currency in Arabic
 */
export const formatArabicCurrency = (
  amount: number | string | undefined | null, 
  currency: string = "جنيه"
): string => {
  if (amount === undefined || amount === null) return "";
  const arabicAmount = convertToArabicNumerals(amount);
  return `${arabicAmount} ${currency}`;
};

/**
 * Format dates properly in Arabic using dd/mm/yyyy format
 */
export const formatArabicDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString();
  
  // Convert to Arabic numerals
  const arabicDay = convertToArabicNumerals(day);
  const arabicMonth = convertToArabicNumerals(month);
  const arabicYear = convertToArabicNumerals(year);
  
  return `${arabicDay}/${arabicMonth}/${arabicYear}`;
};

/**
 * Format dates for English using dd/mm/yyyy format (consistent with Arabic)
 */
export const formatEnglishDate = (dateStr: string): string => {
  const date = new Date(dateStr);
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear().toString();
  
  return `${day}/${month}/${year}`;
};

/**
 * Clean and format Arabic text properly while preserving English words
 */
export const formatArabicText = (text: string): string => {
  if (!text) return '';
  // Only remove special characters but preserve Arabic, English letters, numbers, and common punctuation
  return text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.\,\-\+\%\$\€\£\/\:a-zA-Z]/g, '').trim();
};

/**
 * Smart date formatting that works for both languages with consistent dd/mm/yyyy format
 */
export const formatDateForPDF = (dateStr: string, language: string): string => {
  if (!dateStr) return '';
  
  try {
    if (language === 'ar') {
      return formatArabicDate(dateStr);
    } else {
      return formatEnglishDate(dateStr);
    }
  } catch (error) {
    console.warn('Date formatting error:', error);
    return dateStr; // Return original if formatting fails
  }
};

/**
 * Smart content display - preserves ALL text while applying language-specific number formatting
 */
export const formatMixedContent = (text: string, language: string, type: 'text' | 'currency' | 'date' = 'text'): string => {
  if (!text) return '';
  
  if (type === 'currency') {
    const amount = Number(text);
    if (!isNaN(amount)) {
      return language === 'ar' ? formatArabicCurrency(amount) : Math.round(amount).toString();
    }
  }
  
  if (type === 'date') {
    return formatDateForPDF(text, language);
  }
  
  // For regular text, preserve ALL content - don't clean anything
  // Only apply Arabic numeral conversion if language is Arabic AND text contains numbers
  if (language === 'ar' && /\d/.test(text)) {
    // Convert only the numbers to Arabic numerals, preserve all text
    return text.replace(/\d/g, (digit) => "٠١٢٣٤٥٦٧٨٩"[parseInt(digit)]);
  }
  
  return text; // Return exactly as-is for English or Arabic text without numbers
};

/**
 * Detect if text contains Arabic characters
 */
export const containsArabic = (text: string): boolean => {
  if (!text) return false;
  const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
  return arabicRegex.test(text);
};
