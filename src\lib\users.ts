// src/services/userServices.ts
import { AxiosInstance } from "axios";
import { UserDetails, UserPermissions } from "@/lib/interfaces/userDetails";


interface CreateUserPayload {
  email: string;
  password: string;
  password_confirm: string;
  role: string;
  first_name?: string;
  last_name?: string;
  username?: string;
}

export interface ApiUser {
  user_id: number;
  email: string;
  role: string;
  first_name: string | null;
  last_name: string | null;
  last_active: string | null;
  created_at: string;
  username: string | null;
  permissions: UserPermissions;
}

export interface UpdatePermissionsPayload {
  [module: string]: {
    sidebar?: boolean;
    view?: boolean;
    create?: boolean;
    edit?: boolean;
    delete?: boolean;
    export?: boolean;
    import_data?: boolean;
    approve?: boolean;
    reject?: boolean;
    analytics?: boolean;
    notifications?: boolean;
    view_history?: boolean;
    manage_accounts?: boolean;
    view_activity_log?: boolean;
    view_terms?: boolean;
    manage_templates?: boolean;
  };
}

export interface UpdateUserPayload {
  user: {
    email?: string;
    username?: string;
    role?: string;
    password?: string;
    password_confirm?: string;
    is_active?: boolean;
    first_name?: string;
    last_name?: string;
  };
  permissions: UpdatePermissionsPayload;
}


const createUserServices = (apiClient: AxiosInstance) => ({

  createUser: async (payload: CreateUserPayload): Promise<ApiUser> => {
    try {
      const response = await apiClient.post("/users/api/users/create/", payload);
      return response.data;
    } catch (error) {
      console.error("Error creating user:", error);
      throw error;
    }
  },

  listUsers: async (): Promise<ApiUser[]> => {
    try {
      const response = await apiClient.get("/users/api/users/permissions/list/");
      return response.data;
    } catch (error) {
      console.error("Error fetching users:", error);
      throw error;
    }
  },

  updateUserPermissions: async (
    userId: number,
    payload: UpdateUserPayload
  ): Promise<ApiUser> => {
    try {
      const response = await apiClient.put(
        `/users/api/users/permissions/update/${userId}/`,
        payload
      );
      return response.data;
    } catch (error) {
      console.error("Error updating user permissions:", error);
      throw error;
    }
  },

  getUserById: async (userId: number): Promise<ApiUser> => {
    try {
      const response = await apiClient.get(`/users/api/users/${userId}/`);
      return response.data;
    } catch (error) {
      console.error("Error fetching user:", error);
      throw error;
    }
  },


  transformToUserDetails: (apiUser: ApiUser): UserDetails => {
    return {
      id: apiUser.user_id,
      email: apiUser.email,
      role: apiUser.role,
      username: apiUser.username || "",
      lastActivity: apiUser.last_active || "",
      first_name: apiUser.first_name || "",
      last_name: apiUser.last_name || "",
      created_at: apiUser.created_at,
      password: "", 
      permissions: apiUser.permissions,
    };
  },

 
  transformToApiUser: (userDetails: UserDetails): Partial<ApiUser> => {
    return {
      user_id: userDetails.id,
      email: userDetails.email,
      role: userDetails.role,
      username: userDetails.username || null,
      last_active: userDetails.lastActivity || null,
    };
  },
});

export default createUserServices;