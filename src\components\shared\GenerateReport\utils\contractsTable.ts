import jsPD<PERSON> from "jspdf";
import autoTable from "jspdf-autotable";
import { Contract } from "@/lib/interfaces/contract";
import { FontInfo, PDFField, PDFData } from "../types";
import { getTranslation } from "../utils/translations";
import { formatArabicCurrency, formatDateForPDF, formatMixedContent } from "../utils/formatters";
import { getAutoTableFont } from "../utils/fontHelpers";

/**
 * Generate Contracts Table
 */
export const generateContractsTable = async (
  doc: jsPDF,
  contracts: Contract[],
  language: string,
  startY: number,
  pageWidth: number,
  fields: PDFField[],
  data?: PDFData,
  fontInfo?: FontInfo
): Promise<number> => {
  let currentY = startY;

  // Use the comprehensive translation function
  const t = (key: string) => getTranslation(key, language);

  // Apply field edits if provided
  let processedContracts = [...contracts];
  if (fields && fields.length > 0) {
    processedContracts = contracts.map((contract, index) => {
      const contractFields = fields.filter(f => f.key.startsWith(`contract_${index}_`));
      let updatedContract = { ...contract };

      contractFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedContract.title = field.value;
        if (field.key.endsWith('_total_amount')) updatedContract.total_amount = Number(field.value);
        if (field.key.endsWith('_start_date')) updatedContract.start_date = field.value;
        if (field.key.endsWith('_end_date')) updatedContract.end_date = field.value;
        if (field.key.endsWith('_status')) updatedContract.status = field.value as any;
      });

      return updatedContract;
    });
  }

  // Table columns with proper translations
  const tableColumn = language === "ar"
    ? [t("title"), t("startDate"), t("endDate"), t("amount"), t("status")]
    : [t("title"), t("startDate"), t("endDate"), t("amount"), t("status")];

  // Process contracts with MIXED CONTENT SUPPORT
  const contractRows = processedContracts.map(contract => [
    formatMixedContent(contract.title || "-", language, 'text'), // Preserve original title
    formatDateForPDF(contract.start_date, language), // Consistent date format
    formatDateForPDF(contract.end_date, language), // Consistent date format
    formatMixedContent(
      (Math.round(Number(contract.total_amount) || 0)).toString(), 
      language, 
      'currency'
    ),
    t(contract.status) // Translate status
  ]);

  // Improved title rendering
  doc.setFontSize(14);
  const contractsTitle = t("contracts");
  const titleWidth = doc.getTextWidth(contractsTitle);
  doc.text(contractsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 8; // Reduced spacing

  autoTable(doc, {
    head: [tableColumn],
    body: contractRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontStyle: 'bold', // Always use bold for headers
      halign: language === "ar" ? "right" : "left",
      fillColor: [155, 89, 182], // EXACT original purple
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left" },
      1: { halign: language === "ar" ? "right" : "center" },
      2: { halign: language === "ar" ? "right" : "center" },
      3: { halign: language === "ar" ? "right" : "right" },
      4: { halign: language === "ar" ? "right" : "center" }
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 10;
  const totalContracts = Math.round(processedContracts.reduce((sum, c) => sum + (Number(c.total_amount) || 0), 0));
  const totalContractsText = language === "ar"
    ? `${formatArabicCurrency(totalContracts)} :${t("total contracts")}`
    : `${t("total contracts")}: ${totalContracts}`;
  const contractsTextWidth = doc.getTextWidth(totalContractsText);
  doc.setFontSize(12);
  doc.text(totalContractsText, language === "ar" ? pageWidth - contractsTextWidth - 14 : 14, currentY);

  return currentY + 10;
};
