import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { Reservation } from "@/lib/interfaces/reservation";
import { FontInfo, PDFField, PDFData } from "../types";
import { getTranslation } from "../utils/translations";
import { formatArabicCurrency, formatDateForPDF, formatMixedContent } from "../utils/formatters";
import { getFallbackFont } from "../utils/fontHelpers";

/**
 * Generate Reservations Table
 */
export const generateReservationsTable = async (
  doc: jsPDF,
  reservations: Reservation[],
  language: string,
  startY: number,
  pageWidth: number,
  fields: PDFField[],
  data?: PDFData,
  fontInfo?: FontInfo
): Promise<number> => {
  let currentY = startY;

  // Use the comprehensive translation function
  const t = (key: string) => getTranslation(key, language);

  // Apply field edits if provided
  let processedReservations = [...reservations];
  if (fields && fields.length > 0) {
    processedReservations = reservations.map((reservation, index) => {
      const reservationFields = fields.filter(f => f.key.startsWith(`reservation_${index}_`));
      let updatedReservation = { ...reservation };

      reservationFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedReservation.title = field.value;
        if (field.key.endsWith('_total_amount')) updatedReservation.total_amount = Number(field.value);
        if (field.key.endsWith('_reservationDate')) updatedReservation.reservationDate = field.value;
        if (field.key.endsWith('_status')) updatedReservation.status = field.value as any;
      });

      return updatedReservation;
    });
  }

  // Table columns with proper translations
  const tableColumn = language === "ar"
    ? [t("title"), t("reservationDate"), t("startDate"), t("endDate"), t("amount")]
    : [t("title"), t("reservationDate"), t("startDate"), t("endDate"), t("amount")];

  // Process reservations with MIXED CONTENT SUPPORT
  const reservationRows = processedReservations.map(reservation => [
    formatMixedContent(reservation.title || "-", language, 'text'), // Preserve original title
    formatDateForPDF(reservation.reservationDate, language), // Consistent date format
    formatDateForPDF(reservation.start_date, language), // Consistent date format
    formatDateForPDF(reservation.end_date, language), // Consistent date format
    formatMixedContent(
      (Math.round(Number(reservation.total_amount) || 0)).toString(), 
      language, 
      'currency'
    )
  ]);

  // Improved title rendering
  doc.setFontSize(14);
  const reservationsTitle = t("reservations");
  const titleWidth = doc.getTextWidth(reservationsTitle);
  doc.text(reservationsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 8; // Reduced spacing

  autoTable(doc, {
    head: [tableColumn],
    body: reservationRows,
    startY: currentY + 3, // Reduced gap between title and table
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: fontInfo?.fontName || "defaultFont",
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
      overflow: 'linebreak', // Handle long text properly
      cellWidth: 'wrap' // Allow cells to wrap content
    },
    headStyles: {
      font: fontInfo?.fontName || "defaultFont",
      fontStyle: fontInfo?.hasBold ? "bold" : "normal",
      halign: language === "ar" ? "right" : "left",
      fillColor: [155, 89, 182],
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left", cellWidth: 'auto' }, // Title column - auto width
      1: { halign: language === "ar" ? "right" : "center", cellWidth: 25 }, // Reservation date column - fixed width
      2: { halign: language === "ar" ? "right" : "center", cellWidth: 25 }, // Start date column - fixed width
      3: { halign: language === "ar" ? "right" : "center", cellWidth: 25 }, // End date column - fixed width
      4: { halign: language === "ar" ? "right" : "right", fontStyle: fontInfo?.hasBold ? "bold" : "normal", cellWidth: 25 } // Amount column - fixed width
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 10;
  const totalReservations = Math.round(processedReservations.reduce((sum, r) => sum + (Number(r.total_amount) || 0), 0));
  const totalReservationsText = language === "ar"
    ? `${formatArabicCurrency(totalReservations)} :${t("total reservations")}`
    : `${t("total reservations")}: ${totalReservations}`;
  const reservationsTextWidth = doc.getTextWidth(totalReservationsText);
  doc.setFontSize(12);
  doc.text(totalReservationsText, language === "ar" ? pageWidth - reservationsTextWidth - 14 : 14, currentY);

  return currentY + 10;
};
