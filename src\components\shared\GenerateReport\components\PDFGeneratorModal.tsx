import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>, <PERSON>Off, Edit3, Save, Download, FileText, Settings } from "lucide-react";
import useLanguage from "@/hooks/useLanguage";
import { PDFGeneratorModalProps, PDFField } from "../types";

const PDFGeneratorModal: React.FC<PDFGeneratorModalProps> = ({
  isOpen = false,
  onClose,
  data = {},
  title = 'PDF Report',
  mode = 'events'
}) => {
  // Early return if required props are missing
  if (!onClose || typeof onClose !== 'function') {
    console.error('PDFGeneratorModal: onClose prop is required and must be a function');
    return null;
  }

  const { t, language } = useLanguage();
  const [fields, setFields] = useState<PDFField[]>([]);
  const [editingField, setEditingField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string>("");
  const [selectedLanguage, setSelectedLanguage] = useState<'ar' | 'en'>(language as 'ar' | 'en');
  const [includeCompanyInfo, setIncludeCompanyInfo] = useState(true);
  const [includeLogo, setIncludeLogo] = useState(true);
  const [includeDate, setIncludeDate] = useState(true);
  const [customTitle, setCustomTitle] = useState(title || 'PDF Report');
  const [isGenerating, setIsGenerating] = useState(false);

  // Safe translation function
  const safeT = (key: string, fallback?: string) => {
    try {
      return t(key) || fallback || key;
    } catch (error) {
      console.warn(`Translation error for key: ${key}`, error);
      return fallback || key;
    }
  };

  // Update custom title when title prop changes
  useEffect(() => {
    setCustomTitle(title);
  }, [title]);

  // Initialize fields based on data and mode
  useEffect(() => {
    if (!isOpen || !data) {
      setFields([]);
      return;
    }

    const initializeFields = () => {
      const newFields: PDFField[] = [];

      // Add document metadata fields
      newFields.push({
        key: 'document_title',
        label: safeT('documentTitle', 'Document Title'),
        value: customTitle,
        visible: true,
        editable: true,
        type: 'text',
        category: safeT('documentInfo', 'Document Information')
      });

      // Process EACH EVENT INDIVIDUALLY for editing
      if (mode === 'events' && data.events) {
        data.events.forEach((event, index) => {
          const category = `${safeT('event', 'Event')} ${index + 1}`;
          newFields.push(
            {
              key: `event_${index}_title`,
              label: safeT('title', 'Title'),
              value: event.title || '',
              visible: true,
              editable: true,
              type: 'text',
              category
            },
            {
              key: `event_${index}_amount`,
              label: safeT('amount', 'Amount'),
              value: event.amount?.toString() || '0',
              visible: true,
              editable: true,
              type: 'currency',
              category
            },
            {
              key: `event_${index}_dueDate`,
              label: safeT('dueDate', 'Due Date'),
              value: event.dueDate || '',
              visible: true,
              editable: true,
              type: 'date',
              category
            },
            {
              key: `event_${index}_status`,
              label: safeT('status', 'Status'),
              value: event.status || '',
              visible: true,
              editable: true,
              type: 'text',
              category
            },
            {
              key: `event_${index}_description`,
              label: safeT('description', 'Description'),
              value: event.description || '',
              visible: true,
              editable: true,
              type: 'textarea',
              category
            }
          );
        });
      }

      // Process locations for editing
      if (mode === 'locations' && data.locations) {
        data.locations.forEach((location, index) => {
          const category = `${safeT('location', 'Location')} ${index + 1}`;
          newFields.push(
            {
              key: `location_${index}_name`,
              label: safeT('name', 'Name'),
              value: location.name || '',
              visible: true,
              editable: true,
              type: 'text',
              category
            },
            {
              key: `location_${index}_address`,
              label: safeT('address', 'Address'),
              value: location.address || '',
              visible: true,
              editable: true,
              type: 'text',
              category
            },
            {
              key: `location_${index}_capacity`,
              label: safeT('capacity', 'Capacity'),
              value: location.capacity?.toString() || '',
              visible: true,
              editable: true,
              type: 'number',
              category
            }
          );
        });
      }

      // Similar logic for reservations and contracts can be added here

      setFields(newFields);
    };

    try {
      initializeFields();
    } catch (error) {
      console.error('Error initializing fields:', error);
      setFields([]);
    }
  }, [isOpen, data, mode, customTitle]);

  const toggleFieldVisibility = (key: string) => {
    setFields(prev => prev.map(field =>
      field.key === key ? { ...field, visible: !field.visible } : field
    ));
  };

  const startEditing = (key: string, currentValue: string) => {
    setEditingField(key);
    setTempValue(currentValue);
  };

  const saveEdit = () => {
    if (editingField) {
      setFields(prev => prev.map(field =>
        field.key === editingField ? { ...field, value: tempValue } : field
      ));
      setEditingField(null);
      setTempValue("");
    }
  };

  const cancelEdit = () => {
    setEditingField(null);
    setTempValue("");
  };

  const generatePDF = async () => {
    setIsGenerating(true);
    try {
      // Import the PDF generation function dynamically
      const { generateCustomPDF } = await import('../generatePDF');

      const visibleFields = fields.filter(field => field.visible);
      const pdfOptions = {
        language: selectedLanguage,
        includeCompanyInfo,
        includeLogo,
        includeDate,
        customTitle,
        fields: visibleFields
      };

      await generateCustomPDF(data, pdfOptions, mode);

      // Close modal after successful generation
      onClose();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert(safeT('Error generating PDF. Please try again.', 'Error generating PDF. Please try again.'));
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isOpen) return null;

  // Early return with error boundary
  try {
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-6xl h-[90vh] flex flex-col max-h-[calc(100vh-2rem)] relative">
          {/* Loading Overlay */}
          {isGenerating && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10 rounded-lg">
              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-xl flex items-center space-x-3">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="text-gray-900 dark:text-white font-medium">
                  {safeT('generating', 'Generating...')}
                </span>
              </div>
            </div>
          )}

          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <FileText className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {safeT('pdfGenerator', 'PDF Generator')}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          <div className="flex flex-1 overflow-hidden">
            {/* Settings Panel */}
            <div className="w-80 border-r border-gray-200 dark:border-gray-700 p-6 overflow-y-auto">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                    <Settings className="w-5 h-5 mr-2" />
                    {safeT('pdfSettings', 'PDF Settings')}
                  </h3>

                  {/* Language Selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {safeT('language', 'Language')}
                    </label>
                    <select
                      value={selectedLanguage}
                      onChange={(e) => setSelectedLanguage(e.target.value as 'ar' | 'en')}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    >
                      <option value="ar">{safeT('arabic', 'Arabic')}</option>
                      <option value="en">{safeT('english', 'English')}</option>
                    </select>
                  </div>

                  {/* Custom Title */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      {safeT('documentTitle', 'Document Title')}
                    </label>
                    <input
                      type="text"
                      value={customTitle}
                      onChange={(e) => setCustomTitle(e.target.value)}
                      className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                    />
                  </div>

                  {/* Options */}
                  <div className="space-y-3">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={includeLogo}
                        onChange={(e) => setIncludeLogo(e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {safeT('includeLogo', 'Include Company Logo')}
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={includeCompanyInfo}
                        onChange={(e) => setIncludeCompanyInfo(e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {safeT('includeCompanyInfo', 'Include Company Information')}
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={includeDate}
                        onChange={(e) => setIncludeDate(e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {safeT('includeDate', 'Include Creation Date')}
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>

            {/* Fields Editing Panel */}
            <div className="flex-1 p-6 overflow-y-auto">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
                {safeT('selectAndEditFields', 'Select and Edit Fields')}
              </h3>

              <div className="space-y-6">
                {Object.entries(
                  fields.reduce((acc, field) => {
                    if (!acc[field.category]) {
                      acc[field.category] = [];
                    }
                    acc[field.category].push(field);
                    return acc;
                  }, {} as Record<string, PDFField[]>)
                ).map(([category, categoryFields]) => (
                  <div key={category} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                    <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                      {category}
                    </h4>

                    <div className="space-y-3">
                      {categoryFields.map((field) => (
                        <div key={field.key} className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                          <button
                            onClick={() => toggleFieldVisibility(field.key)}
                            className={`p-1 rounded ${field.visible ? 'text-green-600' : 'text-gray-400'}`}
                          >
                            {field.visible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
                          </button>

                          <div className="flex-1">
                            <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                              {field.label}
                            </div>

                            {editingField === field.key ? (
                              <div className="flex items-center space-x-2 mt-1">
                                {field.type === 'textarea' ? (
                                  <textarea
                                    value={tempValue}
                                    onChange={(e) => setTempValue(e.target.value)}
                                    className="flex-1 p-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                                    rows={2}
                                  />
                                ) : (
                                  <input
                                    type={field.type === 'currency' || field.type === 'number' ? 'number' : field.type === 'date' ? 'date' : 'text'}
                                    value={tempValue}
                                    onChange={(e) => setTempValue(e.target.value)}
                                    className="flex-1 p-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                                  />
                                )}
                                <button
                                  onClick={saveEdit}
                                  className="p-1 text-green-600 hover:bg-green-100 dark:hover:bg-green-900 rounded"
                                >
                                  <Save className="w-4 h-4" />
                                </button>
                                <button
                                  onClick={cancelEdit}
                                  className="p-1 text-red-600 hover:bg-red-100 dark:hover:bg-red-900 rounded"
                                >
                                  <X className="w-4 h-4" />
                                </button>
                              </div>
                            ) : (
                              <div className="flex items-center justify-between mt-1">
                                <span className="text-sm text-gray-600 dark:text-gray-400 truncate">
                                  {field.value || safeT('noValue', 'No Value')}
                                </span>
                                {field.editable && (
                                  <button
                                    onClick={() => startEditing(field.key, field.value)}
                                    className="p-1 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded"
                                  >
                                    <Edit3 className="w-4 h-4" />
                                  </button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {fields.filter(f => f.visible).length} {safeT('fieldsSelected', 'fields selected')} / {fields.length} {safeT('total', 'total')}
            </div>

            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                {safeT('cancel', 'Cancel')}
              </button>
              <button
                onClick={generatePDF}
                disabled={isGenerating}
                className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>{isGenerating ? safeT('generating', 'Generating...') : safeT('generatePDF', 'Generate PDF')}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  } catch (error) {
    console.error('Error rendering PDFGeneratorModal:', error);
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-black bg-opacity-50 p-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 max-w-md">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
            Error Loading PDF Generator
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
            There was an error loading the PDF generator. Please try again.
          </p>
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }
};

export default PDFGeneratorModal;
