import { translate } from "@/utils/translator";

/**
 * Enhanced translation function that uses the main translation system
 */
export const getTranslation = (key: string, language: string): string => {
  // First try the main translation system
  const mainTranslation = translate(key, language);
  if (mainTranslation !== key) {
    return mainTranslation;
  }

  // Fallback translations for PDF-specific terms that might not be in main translations
  const pdfTranslations: Record<string, Record<string, string>> = {
    en: {
      "company_name": "Saray Vera",
      "created": "Created",
      "incomeEvents": "Income Events",
      "expenseEvents": "Expense Events",
      "totalIncome": "Total Income",
      "totalExpenses": "Total Expenses",
      "totalReservations": "Total Reservations",
      "totalContracts": "Total Contracts",
      "total income": "Total Income",
      "total expenses": "Total Expenses",
      "total reservations": "Total Reservations",
      "total contracts": "Total Contracts",
      "startDate": "Start Date",
      "endDate": "End Date",
      "address": "Address",
      "capacity": "Capacity",
      "active": "Active",
      "inactive": "Inactive",
      "contacts": "Contacts",
      "locations": "Locations",
      "reservations": "Reservations",
      "contracts": "Contracts",
      "createdOn": "Created On",
      "allRightsReserved": "All Rights Reserved",
      "page": "Page",
      "document": "Document",
      "report": "Report",
      "title": "Title",
      "amount": "Amount",
      "dueDate": "Due Date",
      "status": "Status",
      "description": "Description",
      "name": "Name",
      "email": "Email",
      "phone": "Phone",
      "company": "Company",
      "pending": "Pending",
      "completed": "Completed",
      "overdue": "Overdue",
      "cancelled": "Cancelled",
      "high": "High",
      "medium": "Medium",
      "low": "Low",
      "client": "Client",
      "property": "Property",
      "monthlyAmount": "Monthly Amount",
      "autoRenewal": "Auto Renewal",
      "rateIncrease": "Rate Increase",
      "noticePeriod": "Notice Period",
      "reservationDate": "Reservation Date",
      "totalAmount": "Total Amount",
      "installments": "Installments",
      "partial": "Partial",
      "full": "Full"
    },
    ar: {
      "company_name": "سراي فيرا",
      "created": "تم الإنشاء في",
      "incomeEvents": "أحداث الدخل",
      "expenseEvents": "أحداث المصروفات",
      "totalIncome": "إجمالي الدخل",
      "totalExpenses": "إجمالي المصروفات",
      "totalReservations": "إجمالي الحجوزات",
      "totalContracts": "إجمالي العقود",
      "total income": "إجمالي الدخل",
      "total expenses": "إجمالي المصروفات",
      "total reservations": "إجمالي الحجوزات",
      "total contracts": "إجمالي العقود",
      "startDate": "تاريخ البداية",
      "endDate": "تاريخ النهاية",
      "address": "العنوان",
      "capacity": "السعة",
      "active": "نشط",
      "inactive": "غير نشط",
      "contacts": "جهات الاتصال",
      "locations": "المواقع",
      "reservations": "الحجوزات",
      "contracts": "العقود",
      "createdOn": "تم الإنشاء في",
      "allRightsReserved": "جميع الحقوق محفوظة",
      "page": "صفحة",
      "document": "مستند",
      "report": "تقرير",
      "title": "العنوان",
      "amount": "المبلغ",
      "dueDate": "تاريخ الاستحقاق",
      "status": "الحالة",
      "description": "الوصف",
      "name": "الاسم",
      "email": "البريد الإلكتروني",
      "phone": "الهاتف",
      "company": "الشركة",
      "pending": "قيد الانتظار",
      "completed": "مكتمل",
      "overdue": "متأخر",
      "cancelled": "ملغى",
      "high": "عالي",
      "medium": "متوسط",
      "low": "منخفض",
      "client": "العميل",
      "property": "العقار",
      "monthlyAmount": "المبلغ الشهري",
      "autoRenewal": "التجديد التلقائي",
      "rateIncrease": "زيادة النسبة",
      "noticePeriod": "فترة الإشعار",
      "reservationDate": "تاريخ الحجز",
      "totalAmount": "المبلغ الإجمالي",
      "installments": "الأقساط",
      "partial": "جزئي",
      "full": "كامل"
    }
  };

  return pdfTranslations[language]?.[key] || key;
};
