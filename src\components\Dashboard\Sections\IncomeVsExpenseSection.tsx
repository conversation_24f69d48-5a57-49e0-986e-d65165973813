import React, { useState, useEffect, useMemo } from "react";
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer, Cell } from "recharts";
import { EventDetails } from "@/lib/interfaces/finaces";
import { useSpring, animated } from '@react-spring/web';
import useLanguage from "@/hooks/useLanguage";
import {
    formatCurrency,
    formatCurrencyCompact,
    formatNumber,
    getChartConfig,
    filterValidEvents,
    CHART_COLORS
} from "@/utils/analyticsUtils";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface IncomeVsExpenseSectionProps {
    events: EventDetails[];
    defaultFilter?: {
        month?: string | null;
        year?: string | null;
        status?: string | null;
    };
}

// Helper function for Arabic numerals
const toLanguageNumerals = (num: number, language: string) => {
    if (language === 'ar') {
        const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return num.toString().replace(/[0-9]/g, (digit) => arabicNumerals[parseInt(digit)]);
    }
    return num.toString();
};

const IncomeVsExpenseSection: React.FC<IncomeVsExpenseSectionProps> = ({ events, defaultFilter }) => {
    const { t, language } = useLanguage();
    const currentDate = new Date();
    const currentMonth = currentDate.toLocaleString("default", { month: "short" });
    const currentYear = currentDate.getFullYear().toString();

    const [selectedMonth, setSelectedMonth] = useState<string | null>(
        defaultFilter?.month !== undefined ? defaultFilter.month : currentMonth
    );
    const [selectedYear, setSelectedYear] = useState<string | null>(
        defaultFilter?.year !== undefined ? defaultFilter.year : currentYear
    );
    const [selectedStatus, setSelectedStatus] = useState<string | null>(
        defaultFilter?.status !== undefined ? defaultFilter.status : null
    );

    useEffect(() => {
        if (defaultFilter) {
            if (defaultFilter.month !== undefined) setSelectedMonth(defaultFilter.month);
            if (defaultFilter.year !== undefined) setSelectedYear(defaultFilter.year);
            if (defaultFilter.status !== undefined) setSelectedStatus(defaultFilter.status);
        }
    }, [defaultFilter]);

    // Process and filter events
    const validEvents = useMemo(() => filterValidEvents(events), [events]);

    const data = useMemo(() => {
        return validEvents.reduce((acc, event) => {
            const date = new Date(event.dueDate);
            const monthYear = date.toLocaleString("default", { month: "short", year: "numeric" });
            const existing = acc.find(item => item.name === monthYear);

            const amount = Number(event.amount) || 0;
            const isCompleted = event.status === 'completed';

            // For non-completed events, treat as expected/forecasted
            const actualAmount = isCompleted ? amount : 0;
            const expectedAmount = !isCompleted ? amount : 0;

            if (existing) {
                if (event.category === "income") {
                    existing.income += amount; // Total (actual + expected)
                    existing.actualIncome = (existing.actualIncome || 0) + actualAmount;
                    existing.expectedIncome = (existing.expectedIncome || 0) + expectedAmount;
                } else if (event.category === "expense") {
                    existing.expense += amount; // Total (actual + expected)
                    existing.actualExpense = (existing.actualExpense || 0) + actualAmount;
                    existing.expectedExpense = (existing.expectedExpense || 0) + expectedAmount;
                }
            } else {
                const newItem: any = {
                    name: monthYear,
                    income: event.category === "income" ? amount : 0,
                    expense: event.category === "expense" ? amount : 0,
                    actualIncome: event.category === "income" ? actualAmount : 0,
                    actualExpense: event.category === "expense" ? actualAmount : 0,
                    expectedIncome: event.category === "income" ? expectedAmount : 0,
                    expectedExpense: event.category === "expense" ? expectedAmount : 0
                };
                acc.push(newItem);
            }
            return acc;
        }, [] as any[]);
    }, [validEvents]);

    const processedData = useMemo(() => {
        return data.map(item => ({
            ...item,
            difference: item.income - item.expense,
            color: item.income - item.expense >= 0 ? CHART_COLORS.income : CHART_COLORS.expense
        }));
    }, [data]);

    const filteredData = useMemo(() => {
        return processedData.filter(item => {
            const [month, year] = item.name.split(" ");
            return (
                (!selectedMonth || month === selectedMonth) &&
                (!selectedYear || year === selectedYear) &&
                (!selectedStatus || validEvents.some(event => {
                    const eventDate = new Date(event.dueDate);
                    const eventMonthYear = eventDate.toLocaleString("default", { month: "short", year: "numeric" });
                    return eventMonthYear === item.name && event.status === selectedStatus;
                }))
            );
        });
    }, [processedData, selectedMonth, selectedYear, selectedStatus, validEvents]);

    const totalIncome = useMemo(() => filteredData.reduce((sum, item) => sum + item.income, 0), [filteredData]);
    const totalExpense = useMemo(() => filteredData.reduce((sum, item) => sum + item.expense, 0), [filteredData]);
    const totalDifference = useMemo(() => totalIncome - totalExpense, [totalIncome, totalExpense]);

    const toNumerals = (num: number) => toLanguageNumerals(num, language);

    const months = useMemo(() => Array.from(new Set(processedData.map(item => item.name.split(" ")[0]))), [processedData]);
    const years = useMemo(() => Array.from(new Set(processedData.map(item => item.name.split(" ")[1]))), [processedData]);
    const statuses = ["completed", "overdue", "upcoming"];

    const singleMonthData = useMemo(() => {
        if (selectedMonth && selectedYear) {
            return validEvents.filter(event => {
                const date = new Date(event.dueDate);
                const month = date.toLocaleString("default", { month: "short" });
                const year = date.getFullYear().toString();
                return month === selectedMonth && year === selectedYear;
            }).map(event => ({
                name: toNumerals(new Date(event.dueDate).getDate()),
                income: event.category === "income" ? Number(event.amount) : 0,
                expense: event.category === "expense" ? Number(event.amount) : 0,
                difference: event.category === "income" ? Number(event.amount) : -Number(event.amount),
                color: event.category === "income" ? CHART_COLORS.income : CHART_COLORS.expense
            }));
        } else {
            return filteredData.map(item => ({
                ...item,
                name: item.name.split(' ')[0] + ' ' + toNumerals(parseInt(item.name.split(' ')[1]))
            }));
        }
    }, [selectedMonth, selectedYear, validEvents, filteredData, toNumerals]);

    const groupedData = useMemo(() => {
        return singleMonthData.reduce((acc: { name: string; income: number; expense: number; difference: number; color: string; }[], item) => {
            const existing = acc.find((entry) => entry.name === item.name);
            if (existing) {
                existing.income += item.income;
                existing.expense += item.expense;
                existing.difference += item.difference;
                // Update color based on final difference
                existing.color = existing.difference >= 0 ? CHART_COLORS.income : CHART_COLORS.expense;
            } else {
                acc.push({ ...item });
            }
            return acc;
        }, []);
    }, [singleMonthData]);

    const { number } = useSpring({
        from: { number: 0 },
        number: totalDifference,
        delay: 200,
        config: { mass: 1, tension: 170, friction: 26 },
    });

    // Chart configuration
    const chartConfig = getChartConfig(language);

    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            const data = payload[0]?.payload;
            return (
                <div className="bg-white p-4 border rounded-lg shadow-lg dark:bg-gray-800 dark:border-gray-700 min-w-[200px]">
                    <p className="font-semibold text-gray-900 dark:text-white mb-3">{label}</p>

                    <div className="space-y-2">
                        <p className="text-sm font-medium" style={{ color: payload[0].color }}>
                            {t("Net Difference")}: {formatCurrency(payload[0].value, language)}
                        </p>

                        {data?.income > 0 && (
                            <p className="text-sm text-green-600 dark:text-green-400">
                                {t("Income")}: {formatCurrency(data.income, language)}
                            </p>
                        )}

                        {data?.expense > 0 && (
                            <p className="text-sm text-red-600 dark:text-red-400">
                                {t("Expense")}: {formatCurrency(data.expense, language)}
                            </p>
                        )}

                        {/* Show status information if available */}
                        <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                                {data?.difference >= 0 ? t("Profit") : t("Loss")}
                            </p>
                        </div>
                    </div>
                </div>
            );
        }
        return null;
    };

    // Trend indicator component
    const TrendIndicator = ({ value }: { value: number }) => {
        const isPositive = value > 0;
        const isNeutral = value === 0;

        return (
            <div className="flex items-center space-x-1">
                {isNeutral ? (
                    <Minus className="w-4 h-4 text-gray-500" />
                ) : isPositive ? (
                    <TrendingUp className="w-4 h-4 text-green-500" />
                ) : (
                    <TrendingDown className="w-4 h-4 text-red-500" />
                )}
            </div>
        );
    };

    if (!events || events.length === 0) {
        return (
            <div className="bg-white p-6 border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700">
                <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-lg mx-auto mb-4 flex items-center justify-center">
                        <TrendingUp className="w-8 h-8 text-gray-400" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        {t("No Financial Data")}
                    </h3>
                    <p className="text-gray-500 dark:text-gray-400">
                        {t("Add income and expense events to see the comparison")}
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-white border rounded-lg shadow-sm dark:bg-gray-800 dark:border-gray-700" dir={language === 'ar' ? 'rtl' : 'ltr'}>
            {/* Header with Summary */}
            <div className="p-6 border-b dark:border-gray-700">
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6">
                    <h2 className="text-xl font-semibold dark:text-white mb-4 lg:mb-0">
                        {t("Income vs Expense")}
                    </h2>

                    {/* Summary Stats */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-gradient-to-r from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-green-600 dark:text-green-400">{t("Total Income")}</p>
                                    <p className="text-lg font-semibold text-green-700 dark:text-green-300">
                                        {formatCurrency(totalIncome, language)}
                                    </p>
                                </div>
                                <TrendIndicator value={totalIncome} />
                            </div>
                        </div>

                        <div className="bg-gradient-to-r from-red-50 to-red-100 dark:from-red-900/20 dark:to-red-800/20 p-4 rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-sm text-red-600 dark:text-red-400">{t("Total Expense")}</p>
                                    <p className="text-lg font-semibold text-red-700 dark:text-red-300">
                                        {formatCurrency(totalExpense, language)}
                                    </p>
                                </div>
                                <TrendIndicator value={-totalExpense} />
                            </div>
                        </div>

                        <div className={`bg-gradient-to-r p-4 rounded-lg ${
                            totalDifference >= 0
                                ? 'from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20'
                                : 'from-orange-50 to-orange-100 dark:from-orange-900/20 dark:to-orange-800/20'
                        }`}>
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <p className={`text-sm ${
                                        totalDifference >= 0
                                            ? 'text-blue-600 dark:text-blue-400'
                                            : 'text-orange-600 dark:text-orange-400'
                                    }`}>
                                        {t("Net Difference")}
                                    </p>
                                    <animated.p className={`text-xl font-bold break-words ${
                                        totalDifference >= 0
                                            ? 'text-blue-700 dark:text-blue-300'
                                            : 'text-orange-700 dark:text-orange-300'
                                    }`} title={formatCurrency(totalDifference, language)}>
                                        {number.to(n => formatCurrencyCompact(n, language))}
                                    </animated.p>
                                    <p className={`text-xs mt-1 ${
                                        totalDifference >= 0
                                            ? 'text-blue-600/70 dark:text-blue-400/70'
                                            : 'text-orange-600/70 dark:text-orange-400/70'
                                    }`}>
                                        {formatCurrency(totalDifference, language)}
                                    </p>
                                </div>
                                <TrendIndicator value={totalDifference} />
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters */}
                <div className="flex flex-wrap gap-4">
                    <select
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={selectedMonth || ""}
                        onChange={e => setSelectedMonth(e.target.value || null)}
                    >
                        <option value="">{t("All Months")}</option>
                        {months.map(month => (
                            <option key={month} value={month}>
                                {t(month)}
                            </option>
                        ))}
                    </select>

                    <select
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={selectedYear || ""}
                        onChange={e => setSelectedYear(e.target.value || null)}
                    >
                        <option value="">{t("All Years")}</option>
                        {years.map(year => (
                            <option key={year} value={year}>
                                {toNumerals(parseInt(year))}
                            </option>
                        ))}
                    </select>

                    <select
                        className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                        value={selectedStatus || ""}
                        onChange={e => setSelectedStatus(e.target.value || null)}
                    >
                        <option value="">{t("All Statuses")}</option>
                        {statuses.map(status => (
                            <option key={status} value={status}>
                                {t(status)}
                            </option>
                        ))}
                    </select>
                </div>
            </div>
            {/* Chart */}
            <div className="p-6">
                <div className="h-96 w-full overflow-hidden">
                    <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                            data={groupedData}
                            margin={{
                                top: 20,
                                right: 30,
                                left: language === 'ar' ? 50 : 40,
                                bottom: language === 'ar' ? 80 : 60
                            }}
                        >
                            <defs>
                                <linearGradient id="positiveGradient" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="5%" stopColor={CHART_COLORS.income} stopOpacity={0.8}/>
                                    <stop offset="95%" stopColor={CHART_COLORS.income} stopOpacity={0.3}/>
                                </linearGradient>
                                <linearGradient id="negativeGradient" x1="0" y1="0" x2="0" y2="1">
                                    <stop offset="5%" stopColor={CHART_COLORS.expense} stopOpacity={0.8}/>
                                    <stop offset="95%" stopColor={CHART_COLORS.expense} stopOpacity={0.3}/>
                                </linearGradient>
                            </defs>
                            <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                            <XAxis
                                dataKey="name"
                                axisLine={false}
                                tickLine={false}
                                tick={{
                                    fontSize: 11,
                                    fill: '#6b7280',
                                    textAnchor: language === 'ar' ? 'start' : 'end',
                                    dy: language === 'ar' ? 10 : 0
                                }}
                                angle={-45}
                                textAnchor={language === 'ar' ? 'start' : 'end'}
                                height={60}
                                interval={0}
                            />
                            <YAxis
                                tickFormatter={(value) => formatNumber(value, language)}
                                axisLine={false}
                                tickLine={false}
                                tick={{
                                    fontSize: 11,
                                    fill: '#6b7280',
                                    dx: language === 'ar' ? -10 : 0
                                }}
                                width={60}
                            />
                            <Tooltip content={<CustomTooltip />} />
                            <Bar
                                dataKey="difference"
                                name={t("Difference")}
                                radius={[4, 4, 0, 0]}
                                maxBarSize={60}
                            >
                                {groupedData.map((entry, index) => (
                                    <Cell
                                        key={`cell-${index}`}
                                        fill={entry.difference >= 0 ? "url(#positiveGradient)" : "url(#negativeGradient)"}
                                    />
                                ))}
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </div>

                {/* Additional Insights */}
                {groupedData.length > 0 && (
                    <div className="mt-6 pt-6 border-t dark:border-gray-700">
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Data Points")}</p>
                                <p className="text-lg font-semibold text-gray-600 dark:text-gray-400">
                                    {groupedData.length}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Positive Periods")}</p>
                                <p className="text-lg font-semibold text-green-600 dark:text-green-400">
                                    {groupedData.filter(item => item.difference > 0).length}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Negative Periods")}</p>
                                <p className="text-lg font-semibold text-red-600 dark:text-red-400">
                                    {groupedData.filter(item => item.difference < 0).length}
                                </p>
                            </div>
                            <div className="text-center">
                                <p className="text-sm text-gray-500 dark:text-gray-400">{t("Best Period")}</p>
                                <p className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                                    {groupedData.reduce((best, current) =>
                                        current.difference > best.difference ? current : best,
                                        groupedData[0]
                                    )?.name || '-'}
                                </p>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default IncomeVsExpenseSection;