import React, { useState, useEffect, useMemo, use } from "react";
import { Reservation } from "@/lib/interfaces/reservation";
import { EventDetails } from "@/lib/interfaces/finaces";
import IncomeServices from "@/lib/income";
import ExpenseServices from "@/lib/expenses";
import ReservationServices from "@/lib/reservations";
import {
  FaEdit,
  FaTrashAlt,
  FaTimes,
  FaDownload,
  FaPrint,
  FaMapMarkerAlt,
  FaUser,
  FaClock,
  FaFileAlt,
  FaExclamationCircle,
  FaChevronDown,
} from "react-icons/fa";
import ReservationForm from "./ReservationForm";
import useLanguage from "@/hooks/useLanguage";
import { SuccessPopup } from "@/components/common/successPopUp";
import { ErrorPopup } from "@/components/common/errorPopUp";

import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import { PDFGeneratorModal } from "@/components/shared/GenerateReport";
import { usePDFGenerator } from "@/hooks/usePDFGenerator";

interface ReservationDetailsProps {
  reservation: Reservation;
  onClose: () => void;
  onUpdate: (reservation: Reservation, events: EventDetails[]) => void;
  onDelete: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
  canExport?: boolean;
}

const statusOptions = [
  "completed",
  "pending",
  "upcoming",
  "overdue",
] as const;

const ReservationDetails: React.FC<ReservationDetailsProps> = ({
  reservation,
  onClose,
  onUpdate,
  onDelete,
  canEdit = true,
  canDelete = true,
  canExport = true,
}) => {
  const { t, language } = useLanguage();
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState<"details" | "installments">(
    "details",
  );
  const [incomeData, setIncomeData] = useState<EventDetails[]>([]);
  const [expenseData, setExpenseData] = useState<EventDetails[]>([]);
  const [isLoadingIncome, setIsLoadingIncome] = useState(false);
  const [incomeError, setIncomeError] = useState<string | null>(null);
  const [hasLoadedIncomeData, setHasLoadedIncomeData] = useState(false);
  const [isFinancialDataReady, setIsFinancialDataReady] = useState(false);
  const [showActualAmount, setShowActualAmount] = useState(false);
  const ReservationService = ReservationServices();
  const [activeEventType, setActiveEventType] = useState<"income" | "expense">(
    "income",
  );
  const [dropdownOpenId, setDropdownOpenId] = useState<string | null>(null);
  const isTotalAmountEqual =
    reservation.total_amount === reservation.actual_amount;
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const { modalProps, openPDFModal, closePDFModal } = usePDFGenerator();

  useEffect(() => {
    const fetchIncomeAndExpenseByReservationId = async () => {
      setIsLoadingIncome(true);
      setIncomeError(null);
      try {
        const response = await ReservationService.getReservationEventsById(
          reservation.id,
        );
        console.log("Fetched income and expense data:", response);
        if (response.incomes) {
          setIncomeData(response.incomes);
        }
        if (response.expenses) {
          setExpenseData(response.expenses);
        }
        setIsFinancialDataReady(true);
      } catch (error) {
        console.error("Error fetching income and expense data:", error);
        setIncomeError(t("Failed to load income data"));
      }
      setIsLoadingIncome(false);
      setHasLoadedIncomeData(true);
    };
    if (!hasLoadedIncomeData) {
      fetchIncomeAndExpenseByReservationId();
    }
  }, [reservation.id, hasLoadedIncomeData, ReservationService]);

  useEffect(() => {
    setHasLoadedIncomeData(false);
    setIncomeData([]);
    setIsFinancialDataReady(false);
  }, [reservation.id]);

  const safeValue = (value: any, defaultValue: any = "N/A") => {
    return value !== undefined && value !== null ? value : defaultValue;
  };

  const formatDate = (dateString: string | undefined) => {
    return dateString
      ? new Date(dateString).toLocaleDateString(
          language === "ar" ? "ar-EG" : "en-US",
        )
      : "N/A";
  };

  const formatCurrency = (amount: number | undefined) => {
    return amount !== undefined && amount !== null
      ? new Intl.NumberFormat(language === "ar" ? "ar-EG" : "en-US", {
          style: "currency",
          currency: "EGP",
          minimumFractionDigits: 0,
        }).format(amount)
      : "N/A";
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "pending":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100";
      case "completed":
        return "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100";
      case "cancelled":
        return "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100";
      case "upcoming":
        return "bg-indigo-100 text-indigo-800 dark:bg-indigo-800 dark:text-indigo-100";
      case "overdue":
        return "bg-orange-100 text-orange-800 dark:bg-orange-800 dark:text-orange-100";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-100";
    }
  };

  const handleEventStatusChange = async (
    eventId: string,
    newStatus: string,
    type: "income" | "expense",
  ) => {
    try {
      if (type === "income") {
        await IncomeServices().updateIncomeStatus(eventId, newStatus);
        setIncomeData((prev) =>
          prev.map((ev) =>
            ev.id === eventId
              ? { ...ev, status: newStatus as EventDetails["status"] }
              : ev,
          ),
        );
      } else {
        await ExpenseServices().updateExpenseStatus(eventId, newStatus);
        setExpenseData((prev) =>
          prev.map((ev) =>
            ev.id === eventId
              ? { ...ev, status: newStatus as EventDetails["status"] }
              : ev,
          ),
        );
      }
      setSuccessMessage(t("Status updated successfully!"));
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (err) {
      setErrorMessage(t("Failed to update status."));
      setShowError(true);
      setTimeout(() => setShowError(false), 2500);
    }
  };

  const { totalPaid, totalRemaining } = useMemo(() => {
    const installmentsPaid = (reservation.installments || [])
      .filter((inst) => inst.status === "completed")
      .reduce((sum, inst) => sum + (Number(inst.amount) || 0), 0);

    const incomePaid = incomeData
      .filter((event) => event.status === "completed")
      .reduce((sum, event) => sum + (Number(event.amount) || 0), 0);

    const paid = installmentsPaid + incomePaid;
    const remaining = (Number(reservation.total_amount) || 0) - paid;

    return { totalPaid: paid, totalRemaining: remaining };
  }, [reservation.installments, incomeData, reservation.total_amount]);

  const totalIncome = incomeData.reduce(
    (sum, event) => sum + (Number(event.amount) || 0),
    0,
  );
  const paidIncome = incomeData
    .filter((event) => event.status === "completed")
    .reduce((sum, event) => sum + (Number(event.amount) || 0), 0);

  const totalActualIncome = incomeData.reduce(
    (sum, event) => sum + (Number(event.actual_amount) || 0),
    0,
  );

  const totalExpense = expenseData.reduce(
    (sum, event) => sum + (Number(event.amount) || 0),
    0,
  );
  const paidExpense = expenseData
    .filter((event) => event.status === "completed")
    .reduce((sum, event) => sum + (Number(event.amount) || 0), 0);

  const totalActualExpense = expenseData.reduce(
    (sum, event) => sum + (Number(event.actual_amount) || 0),
    0,
  );

  const handleDownload = () => {
    // Open the new PDF generator modal for reservations with associated incomes/expenses
    openPDFModal(
      {
        reservations: [reservation],
        events: [...incomeData, ...expenseData] // Include all associated events
      },
      `${t("reservationReport")} - ${reservation.title}`,
      'reservations'
    );
  };

  const handleDelete = async () => {
    if (reservation && reservation.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        await ReservationService.softDeleteReservation(
          reservation.id.toString(),
        );
        // Toast is now handled in the service, so we don't need to show it here
        onDelete(); // Update UI after successful deletion
        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        console.error("Error deleting reservation:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting reservation";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete reservation. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const getReservationDuration = (start_date: string, end_date: string) => {
    const start = new Date(start_date);
    const end = new Date(end_date);
    const months =
      (end.getFullYear() - start.getFullYear()) * 12 +
      (end.getMonth() - start.getMonth());
    return Math.max(0, months);
  };

  if (isEditing) {
    return (
      <ReservationForm
        reservation={reservation}
        onSave={(updatedReservation, events) => {
          onUpdate(updatedReservation, events);
          setIsEditing(false);
        }}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800 sm:p-6">
      <div id="printable-reservation">
        <div className="mb-4 flex flex-col items-start justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center">
          <div>
            <h2 className="mb-1 text-xl font-bold text-gray-900 dark:text-white sm:text-2xl">
              {safeValue(reservation.title)}
            </h2>
            <div className="flex flex-wrap items-center gap-2">
              <span
                className={`rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(reservation.status || "pending")}`}
              >
                {t(
                  safeValue(reservation.status, "pending")
                    .charAt(0)
                    .toUpperCase() +
                    safeValue(reservation.status, "pending").slice(1),
                )}
              </span>
            </div>
          </div>
          <div className="mt-2 flex flex-wrap gap-2 sm:mt-0">
            {canExport && (
            <button
              onClick={handleDownload}
              disabled={isLoadingIncome}
              className={`rounded-lg p-2 ${
                isLoadingIncome
                  ? "cursor-not-allowed bg-gray-300 text-gray-500 dark:bg-gray-600 dark:text-gray-400"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              }`}
              title={
                isLoadingIncome ? t("Loading events...") : t("Download as PDF")
              }
              aria-label={
                isLoadingIncome ? t("Loading events...") : t("Download as PDF")
              }
            >
              <FaDownload size={18} />
            </button>
            )}
            {canEdit && (
            <button
              onClick={() => setIsEditing(true)}
              className="rounded-lg bg-blue-100 p-2 text-blue-700 hover:bg-blue-200 dark:bg-blue-700 dark:text-blue-200 dark:hover:bg-blue-600"
              title={t("Edit Reservation")}
              aria-label={t("Edit Reservation")}
            >
              <FaEdit size={18} />
            </button>
            )}
            {canDelete && (
            <button
              onClick={() => setIsDeleteModalOpen(true)}
              className="rounded-lg bg-red-100 p-2 text-red-700 hover:bg-red-200 dark:bg-red-700 dark:text-red-200 dark:hover:bg-red-600"
              title={t("Delete Reservation")}
              aria-label={t("Delete Reservation")}
            >
              <FaTrashAlt size={18} />
            </button>
            )}
            <button
              onClick={onClose}
              className="rounded-lg bg-gray-100 p-2 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              title={t("Close")}
              aria-label={t("Close")}
            >
              <FaTimes size={18} />
            </button>
          </div>
        </div>

        <div className="mb-6 overflow-x-auto border-b border-gray-200 dark:border-gray-700">
          <nav className="flex min-w-full whitespace-nowrap" aria-label="Tabs">
            <button
              onClick={() => setActiveTab("details")}
              className={`flex-1 border-b-2 px-3 py-3 text-xs font-medium sm:flex-none sm:px-4 sm:text-sm ${
                activeTab === "details"
                  ? "border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:text-gray-300"
              }`}
            >
              {t("Details")}
            </button>
            <button
              onClick={() => setActiveTab("installments")}
              className={`flex-1 border-b-2 px-3 py-3 text-xs font-medium sm:flex-none sm:px-4 sm:text-sm ${
                activeTab === "installments"
                  ? "border-blue-500 text-blue-600 dark:border-blue-400 dark:text-blue-400"
                  : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:border-gray-600 dark:hover:text-gray-300"
              }`}
            >
              {t("Installments")}
            </button>
          </nav>
        </div>
        <div className="mb-6 grid grid-cols-1 gap-3 sm:grid-cols-3 sm:gap-4">
          <div className="rounded-lg bg-green-50 p-3 dark:bg-green-900 sm:p-4">
            <h3 className="mb-1 text-sm font-medium text-green-800 dark:text-green-200">
              {t("Total Amount")}
            </h3>
            <p className="text-lg font-bold text-green-900 dark:text-green-100 sm:text-2xl">
              {showActualAmount
                ? formatCurrency(reservation.actual_amount)
                : formatCurrency(reservation.total_amount)}
            </p>
          </div>
          <div className="rounded-lg bg-blue-50 p-3 dark:bg-blue-900 sm:p-4">
            <h3 className="mb-1 text-sm font-medium text-blue-800 dark:text-blue-200">
              {t("Amount Paid")}
            </h3>
            {!isFinancialDataReady ? (
              <div className="flex h-8 items-center justify-center">
                <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-blue-500"></div>
              </div>
            ) : (
              <p className="text-lg font-bold text-blue-900 dark:text-blue-100 sm:text-2xl">
                {formatCurrency(totalPaid)}
              </p>
            )}
          </div>
          <div className="rounded-lg bg-yellow-50 p-3 dark:bg-yellow-900 sm:p-4">
            <h3 className="mb-1 text-sm font-medium text-yellow-800 dark:text-yellow-200">
              {t("Remaining")}
            </h3>
            {!isFinancialDataReady ? (
              <div className="flex h-8 items-center justify-center">
                <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-yellow-500"></div>
              </div>
            ) : (
              <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100 sm:text-2xl">
                {formatCurrency(totalRemaining)}
              </p>
            )}
          </div>
        </div>

        {activeTab === "details" && (
          <div className="grid grid-cols-1 gap-6">
            <div>
              <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
                {t("Basic Information")}
              </h3>
              <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-start">
                    <FaFileAlt
                      className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                      size={16}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t("Description")}
                      </p>
                      <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                        {safeValue(
                          reservation.description,
                          t("No description provided"),
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center">
                    <FaClock
                      className="mr-2 text-gray-500 dark:text-gray-400"
                      size={16}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t("Reservation Period")}
                      </p>
                      <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                        {formatDate(reservation.start_date)} -{" "}
                        {formatDate(reservation.end_date)}
                        <span className="ml-2 text-xs text-gray-500 dark:text-gray-400">
                          (
                          {Number(
                            getReservationDuration(
                              reservation.start_date,
                              reservation.end_date,
                            ),
                          ).toLocaleString(
                            language === "ar" ? "ar-EG" : "en-US",
                          )}{" "}
                          {t("months")})
                        </span>
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <FaFileAlt
                      className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                      size={16}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t("Notes")}
                      </p>
                      <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                        {reservation.notes || t("No notes")}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
                {t("Advertisement Details")}
              </h3>
              <div className="rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:p-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="flex items-center">
                    <FaUser
                      className="mr-2 text-gray-500 dark:text-gray-400"
                      size={16}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t("Client")}
                      </p>
                      <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                        {safeValue(reservation.contact?.name)}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {safeValue(reservation.contact?.email, "")}{" "}
                        {reservation.contact?.email &&
                        reservation.contact?.phone
                          ? " | "
                          : ""}{" "}
                        {safeValue(reservation.contact?.phone, "")}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <FaMapMarkerAlt
                      className="mr-2 mt-1 text-gray-500 dark:text-gray-400"
                      size={16}
                    />
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">
                        {t("Location")}
                      </p>
                      <p className="text-base font-medium text-gray-900 dark:text-gray-200">
                        {reservation.location.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {reservation.location.address},{" "}
                        {reservation.location.city},{" "}
                        {reservation.location.country}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === "installments" && (
          <div>
            <h3 className="mb-3 text-lg font-semibold text-gray-800 dark:text-gray-200 sm:mb-4">
              {t("Payment Installments")}
            </h3>
            <div className="mb-4 flex gap-2">
              <button
                className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
                  activeEventType === "income"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
                }`}
                onClick={() => setActiveEventType("income")}
              >
                {t("Income")}
              </button>
              <button
                className={`rounded-t-lg px-4 py-2 text-sm font-medium transition-colors ${
                  activeEventType === "expense"
                    ? "bg-blue-500 text-white"
                    : "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-200"
                }`}
                onClick={() => setActiveEventType("expense")}
              >
                {t("Expense")}
              </button>
            </div>

            {isLoadingIncome ? (
              <div className="flex items-center justify-center p-8">
                <div className="h-12 w-12 animate-spin rounded-full border-b-2 border-primary"></div>
              </div>
            ) : incomeError ? (
              <div className="mb-4 flex items-center rounded-lg bg-red-100 p-4 text-red-700 dark:bg-red-900 dark:text-red-200">
                <FaExclamationCircle className="mr-2" />
                {incomeError}
              </div>
            ) : (
              <>
                <div className="mb-6 hidden overflow-x-auto sm:block">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th
                          className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                        >
                          {t("Title")}
                        </th>
                        <th
                          className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                        >
                          {t("Amount")}
                        </th>
                        <th
                          className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                        >
                          {t("Date")}
                        </th>
                        <th
                          className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                        >
                          {t("Type")}
                        </th>
                        <th
                          className={`px-6 py-3 text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-300 ${language === "ar" ? "text-right" : "text-left"}`}
                        >
                          {t("Status")}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                      {(activeEventType === "income" ? incomeData : expenseData)
                        .length > 0 ? (
                        (activeEventType === "income"
                          ? incomeData
                          : expenseData
                        ).map((event) => (
                          <tr
                            key={event.id}
                            className="hover:bg-gray-50 dark:hover:bg-gray-700"
                          >
                            <td className="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900 dark:text-white">
                              {event.title}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {showActualAmount && activeEventType === "income"
                                ? formatCurrency(event.actual_amount)
                                : formatCurrency(event.amount)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {formatDate(event.dueDate)}
                            </td>
                            <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                              {event.type || t("Payment")}
                            </td>
                            <td className="relative whitespace-nowrap px-6 py-4">
                              <button
                                className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-5 ${getStatusBadgeClass(event.status || "completed")}`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setDropdownOpenId(
                                    event.id === dropdownOpenId
                                      ? null
                                      : event.id,
                                  );
                                }}
                                type="button"
                              >
                                {t(
                                  (event.status || "completed")
                                    .charAt(0)
                                    .toUpperCase() +
                                    (event.status || "completed").slice(1),
                                )}
                                <FaChevronDown className="ml-1 h-3 w-3" />
                              </button>
                              {dropdownOpenId === event.id && (
                                <div className="absolute left-0 z-10 mt-1 min-w-[120px] rounded border border-gray-200 bg-white shadow-lg dark:border-gray-700 dark:bg-gray-900">
                                  {statusOptions.map((status) => (
                                    <button
                                      key={status}
                                      className={`block w-full px-3 py-2 text-left text-xs hover:bg-gray-100 dark:hover:bg-gray-800 ${
                                        event.status === status
                                          ? "font-bold text-blue-600 dark:text-blue-400"
                                          : "text-gray-700 dark:text-gray-200"
                                      }`}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEventStatusChange(
                                          event.id,
                                          status,
                                          activeEventType,
                                        );
                                        setDropdownOpenId(null);
                                      }}
                                    >
                                      {t(status)}
                                    </button>
                                  ))}
                                </div>
                              )}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td
                            colSpan={5}
                            className="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400"
                          >
                            {t(
                              `No ${activeEventType} data found for this reservation`,
                            )}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                <div className="mb-6 space-y-3 sm:hidden">
                  {(activeEventType === "income" ? incomeData : expenseData)
                    .length > 0 ? (
                    (activeEventType === "income"
                      ? incomeData
                      : expenseData
                    ).map((event) => (
                      <div
                        key={event.id}
                        className="rounded-lg border border-gray-200 bg-white p-4 shadow-sm dark:border-gray-700 dark:bg-gray-800"
                      >
                        <div className="mb-2 flex items-start justify-between">
                          <span className="font-medium text-gray-900 dark:text-white">
                            {t("Title")} #{event.title}
                          </span>
                          <span
                            className={`rounded-full px-2 py-1 text-xs font-semibold ${getStatusBadgeClass(event.status || "completed")}`}
                          >
                            {t(
                              (event.status || "completed")
                                .charAt(0)
                                .toUpperCase() +
                                (event.status || "completed").slice(1),
                            )}
                          </span>
                        </div>
                        <div className="mt-2 space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {t("Amount")}:
                            </span>
                            <span className="font-medium text-gray-900 dark:text-white">
                              {showActualAmount && activeEventType === "income"
                                ? formatCurrency(event.actual_amount)
                                : formatCurrency(event.amount)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {t("Date")}:
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              {formatDate(event.dueDate)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500 dark:text-gray-400">
                              {t("Type")}:
                            </span>
                            <span className="text-gray-900 dark:text-white">
                              {event.type || t("Payment")}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="py-4 text-center text-gray-500 dark:text-gray-400">
                      {t(
                        `No ${activeEventType} data found for this reservation`,
                      )}
                    </div>
                  )}
                </div>
              </>
            )}

            <div className="mt-4 rounded-lg bg-gray-50 p-3 dark:bg-gray-700 sm:mt-6 sm:p-4">
              <h4 className="text-md mb-2 font-semibold text-gray-800 dark:text-gray-200">
                {activeEventType === "income"
                  ? t("Income Progress")
                  : t("Expense Progress")}
              </h4>
              {!isFinancialDataReady ? (
                <div className="flex h-10 items-center justify-center">
                  <div className="h-6 w-6 animate-spin rounded-full border-b-2 border-primary"></div>
                </div>
              ) : (
                <>
                  {activeEventType === "income"
                    ? (() => {
                        const totalIncome = incomeData.reduce(
                          (sum, event) => sum + (Number(event.amount) || 0),
                          0,
                        );
                        const paidIncome = incomeData
                          .filter((event) => event.status === "completed")
                          .reduce(
                            (sum, event) => sum + (Number(event.amount) || 0),
                            0,
                          );
                        const percent = totalIncome
                          ? (paidIncome / totalIncome) * 100
                          : 0;
                        return (
                          <>
                            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                              <div
                                className="h-2.5 rounded-full bg-blue-600"
                                style={{ width: `${percent}%` }}
                              ></div>
                            </div>
                            <div className="mt-2 flex flex-wrap justify-between text-xs text-gray-500 dark:text-gray-400">
                              <span>
                                {t("Paid")}: {percent.toFixed(0)}%
                              </span>
                              <span>
                                {t("Total")}:{" "}
                                {showActualAmount
                                  ? formatCurrency(
                                      incomeData.reduce(
                                        (sum, event) =>
                                          sum +
                                          (Number(event.actual_amount) || 0),
                                        0,
                                      ),
                                    )
                                  : formatCurrency(totalIncome)}
                              </span>
                            </div>
                          </>
                        );
                      })()
                    : (() => {
                        const totalExpense = expenseData.reduce(
                          (sum, event) => sum + (Number(event.amount) || 0),
                          0,
                        );
                        const paidExpense = expenseData
                          .filter((event) => event.status === "completed")
                          .reduce(
                            (sum, event) => sum + (Number(event.amount) || 0),
                            0,
                          );
                        const percent = totalExpense
                          ? (paidExpense / totalExpense) * 100
                          : 0;
                        return (
                          <>
                            <div className="h-2.5 w-full rounded-full bg-gray-200 dark:bg-gray-600">
                              <div
                                className="h-2.5 rounded-full bg-red-600"
                                style={{ width: `${percent}%` }}
                              ></div>
                            </div>
                            <div className="mt-2 flex flex-wrap justify-between text-xs text-gray-500 dark:text-gray-400">
                              <span>
                                {t("Paid")}: {percent.toFixed(0)}%
                              </span>
                              <span>
                                {t("Total")}:{" "}
                                {showActualAmount
                                  ? formatCurrency(
                                      expenseData.reduce(
                                        (sum, event) =>
                                          sum +
                                          (Number(event.actual_amount) || 0),
                                        0,
                                      ),
                                    )
                                  : formatCurrency(totalExpense)}
                              </span>
                            </div>
                          </>
                        );
                      })()}
                </>
              )}
            </div>
          </div>
        )}
      </div>
      {showSuccess && (
        <SuccessPopup
          message={successMessage || t("Status updated successfully!")}
          onClose={() => setShowSuccess(false)}
        />
      )}
      {showError && (
        <ErrorPopup
          message={errorMessage || t("Failed to update status.")}
          onClose={() => setShowError(false)}
        />
      )}
      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDeleteError(null);
        }}
        onConfirm={handleDelete}
        message={`${t("deleteReservationWarning")} ${t("deleteReservationConditions")}`}
        title={t("deleteReservation")}
        itemName={reservation?.title || ""}
        isLoading={isDeleting}
        error={deleteError}
      />
      {/* PDF Generator Modal - NEW ADVANCED PDF GENERATOR */}
      <PDFGeneratorModal
        isOpen={modalProps.isOpen}
        onClose={modalProps.onClose}
        data={modalProps.data}
        title={modalProps.title}
        mode={modalProps.mode}
      />
    </div>
  );
};

export default ReservationDetails;
