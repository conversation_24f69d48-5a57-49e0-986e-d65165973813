import React, { useEffect, useState } from "react";
import {
  Mail,
  Phone,
  MapPin,
  Edit2,
  Trash2,
  X,
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  History,
  PersonStanding,
} from "lucide-react";
import {
  <PERSON>,
  CardContent,
  <PERSON>Header,
  CardTitle,
} from "@/components/cards/card";
import { Contact, contactLoaction } from "@/lib/types/contacts";
import { Button } from "@/components/ui/button";
import useLanguage from "@/hooks/useLanguage";
import { useRouter } from "next/navigation";
import { toast } from "react-hot-toast";
import { useContactServices } from "@/hooks/useContact";
import { EventDetails } from "@/lib/interfaces/finaces";
import UpcomingEventsPage from "../Dashboard/UpcommingEventsPage/UpcommingEvents";
import { usePDFGenerator } from "@/hooks/usePDFGenerator";
import { PDFGeneratorModal } from "@/components/shared/GenerateReport";

import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import { Reservation } from "@/lib/interfaces/reservation";
import ReservationList from "../Reservations/ReservationList";
import { SuccessPopup } from "../common/successPopUp";
import { ErrorPopup } from "../common/errorPopUp";

interface ContactDetailsProps {
  contact: Contact;
  onEdit: () => void;
  onDelete: () => void;
  onClose: () => void;
  canEdit?: boolean;
  canDelete?: boolean;
  canExport?: boolean;
}

const LocationModal = ({
  locations,
  title,
  onClose,
}: {
  locations: string[];
  title: string;
  onClose: () => void;
}) => (
  <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 p-4">
    <div className="w-full max-w-md rounded-lg bg-white p-4 dark:bg-boxdark sm:p-6">
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-lg font-bold dark:text-gray-100 sm:text-xl">
          {title}
        </h2>
        <button
          onClick={onClose}
          className="rounded-full p-1 text-gray-600 transition-colors hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-100"
        >
          <X size={20} className="sm:size-6" />
        </button>
      </div>
      <div className="max-h-60 space-y-2 overflow-y-auto sm:max-h-80">
        {locations.map((location, index) => (
          <div
            key={index}
            className="rounded bg-gray-100 p-2.5 text-sm dark:bg-gray-700 dark:text-gray-100 sm:p-3 sm:text-base"
          >
            {location}
          </div>
        ))}
      </div>
    </div>
  </div>
);

export const ContactDetails = ({
  contact,
  onEdit,
  onDelete,
  onClose,
  canEdit = true,
  canDelete = true,
  canExport = true,
}: ContactDetailsProps) => {
  const { t, language } = useLanguage();
  const router = useRouter();
  const contactService = useContactServices();
  const { modalProps, openPDFModal } = usePDFGenerator();
  const [locationModalType, setLocationModalType] = useState<
    "shared" | "owned" | null
  >(null);
  const [activeTab, setActiveTab] = useState<"incomes" | "expenses" | "all">(
    "incomes",
  );
  const [contactEvents, setContactEvents] = useState<EventDetails[]>([]);
  const [isLoadingEvents, setIsLoadingEvents] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [showReservations, setShowReservations] = useState(false);
  const [showFinances, setShowFinances] = useState(false);
  const [isSuccessPopup, setSuccessPopup] = useState(false);
  const [isErrorPopup, setErrorPopup] = useState(false);
  const [message, setMessage] = useState("");
  const [contactReservations, setContactReservations] = useState<Reservation[]>(
    [],
  );
  useEffect(() => {
    if (!contact) return;

    // Fetch contact finances with loading state
    setIsLoadingEvents(true);
    contactService
      .getContactFinances(contact.id)
      .then((data) => {
        setContactEvents([...data.incomes, ...data.expenses]);
      })
      .catch((error) => {
        console.error("Error fetching contact finances:", error);
        toast.error(t("errorFetchingContactFinances"));
      })
      .finally(() => {
        setIsLoadingEvents(false);
      });
  }, [contact]);

  useEffect(() => {
    if (!contact) return;
    contactService
      .getContactReservations(contact.id)
      .then((data) => {
        setContactReservations(data);
      })
      .catch(() => {
        toast.error(t("errorFetchingContactReservations"));
      });
  }, [contact]);

  if (!contact) {
    return (
      <Card className="h-full w-full">
        <CardContent className="flex flex-col items-center justify-center py-16">
          <PersonStanding className="h-16 w-16 text-gray-300 dark:text-gray-600" />
          <p className="mt-4 text-center text-gray-500 dark:text-gray-400">
            {t("noContactSelected")}
          </p>
        </CardContent>
      </Card>
    );
  }

  const getBalanceIndicator = () => {
    const balance = Number(contact.balance);
    // Invert the logic: negative balance becomes positive (green), positive becomes negative (red)
    const invertedBalance = balance * -1;
    if (invertedBalance > 0)
      return <ArrowUpRight className="text-green-500" size={20} />;
    if (invertedBalance < 0)
      return <ArrowDownRight className="text-red-500" size={20} />;
    return <Minus className="text-blue-500" size={20} />;
  };

  const renderLocations = (
    locations: contactLoaction[],
    type: "shared" | "owned",
  ) => (
    <div className="space-y-2 sm:space-y-3">
      {locations.slice(0, 3).map((loc, i) => (
        <div
          key={i}
          className="rounded-md bg-white p-3 text-sm shadow-sm dark:bg-gray-700 dark:text-gray-100 sm:p-4 sm:text-base"
        >
          <div className="font-medium text-gray-900 dark:text-white">
            {loc.name}
          </div>
          <div className="mt-1 text-gray-600 dark:text-gray-300">
            {loc.address}
          </div>
        </div>
      ))}
      {locations.length > 3 && (
        <button
          onClick={() => setLocationModalType(type)}
          className="rounded-md bg-blue-50 px-3 py-2 text-sm font-medium text-blue-600 transition-colors hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/40 sm:text-base"
        >
          {t("viewMore")} ({locations.length - 3})
        </button>
      )}
    </div>
  );

  const handleDelete = async () => {
    if (contact && contact.id) {
      setIsDeleting(true);
      setDeleteError(null);

      try {
        await contactService.softDeleteContact(contact.id);

        setMessage(t("contactDeletedSuccessfully"));
        setSuccessPopup(true);
        onDelete();

        setIsDeleteModalOpen(false); // Close modal
      } catch (error: any) {
        setMessage(t("errorDeletingContact"));
        setErrorPopup(true);
        console.error("Error deleting contact:", error);
        // Handle error response for the modal display
        if (error.response && error.response.data) {
          const errorMessage =
            error.response.data.details ||
            error.response.data.error ||
            error.response.data.warning ||
            error.response.data.message ||
            "Error deleting contact";
          setDeleteError(errorMessage);
        } else {
          setDeleteError("Failed to delete contact. Please try again.");
        }
      } finally {
        setIsDeleting(false);
      }
    }
  };

  return (
    <>
      {isSuccessPopup && (
        <SuccessPopup
          message={message}
          onClose={() => setSuccessPopup(false)}
        />
      )}
      {isErrorPopup && (
        <ErrorPopup message={message} onClose={() => setErrorPopup(false)} />
      )}

      <div className="relative w-full">
        <Card className="relative w-full">
          <button
            onClick={onClose}
            className={`absolute ${
              language === "ar" ? "left-3 sm:left-4" : "right-3 sm:right-4"
            } top-3 rounded-full p-1.5 text-gray-400 transition-colors hover:bg-gray-100 hover:text-gray-600 dark:text-gray-500 dark:hover:bg-gray-700 dark:hover:text-white sm:top-4 sm:p-2`}
          >
            <X size={20} className="sm:size-6" />
          </button>
          <CardHeader className="border-b p-4 dark:border-gray-700 sm:p-6">
            <CardTitle className="pr-12 text-lg font-semibold text-gray-900 dark:text-white sm:text-xl lg:text-2xl">
              {t("contactDetails")}
            </CardTitle>
          </CardHeader>

          <CardContent className="space-y-6 p-4 pt-6 sm:space-y-8 sm:p-6">
            {/* Balance and Identity */}
            <div className="flex flex-col gap-4 rounded-lg bg-gray-50 p-4 dark:bg-gray-800/50 sm:flex-row sm:items-start sm:gap-6 sm:p-6">
              <div className="flex items-center gap-3 sm:flex-col sm:items-center sm:gap-2">
                {getBalanceIndicator()}
                <span className="text-xs text-gray-500 dark:text-gray-400 sm:text-center">
                  {t("balance")}
                </span>
              </div>
              <div className="flex-1 space-y-4">
                <div className="flex flex-col gap-4 sm:flex-row sm:items-start sm:justify-between">
                  <div className="min-w-0 flex-1">
                    <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white sm:text-xl lg:text-2xl">
                        {contact.name}
                      </h3>
                      <span className="inline-flex w-fit rounded-full bg-blue-100 px-2.5 py-1 text-xs font-medium text-blue-800 dark:bg-blue-800 dark:text-white sm:text-sm">
                        {contact.type}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300 sm:text-base">
                      {contact.company}
                    </p>
                  </div>

                  <div className="flex flex-col items-start sm:items-end">
                    <p className="text-xs font-medium text-gray-500 dark:text-gray-400 sm:text-sm">
                      {t("current balance")}
                    </p>
                    <div
                      className={`text-xl font-bold sm:text-2xl lg:text-3xl ${
                        // Invert the color logic: negative balance (debt) becomes positive (green), positive becomes negative (red)
                        contact.balance < 0
                          ? "text-green-600 dark:text-green-400"
                          : contact.balance > 0
                            ? "text-red-600 dark:text-red-400"
                            : "text-blue-600 dark:text-blue-400"
                      }`}
                    >
                      <span
                        style={{ direction: "ltr", unicodeBidi: "isolate" }}
                        className="inline-flex"
                      >
                        {/* Show inverted balance: negative becomes positive, positive becomes negative */}
                        {contact.balance < 0
                          ? "+"
                          : contact.balance > 0
                            ? "-"
                            : ""}
                        {Math.abs(Number(contact.balance)).toLocaleString(
                          language === "ar" ? "ar-EG" : "en-US",
                          {
                            minimumFractionDigits: 0,
                            maximumFractionDigits: 0,
                          },
                        )}{" "}
                        <span className="text-sm sm:text-base lg:text-lg">
                          {t("EGP")}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex flex-col gap-2 sm:flex-row sm:justify-end sm:gap-3">
                  {canEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onEdit}
                    className="border-blue-200 text-blue-600 dark:border-blue-800 dark:hover:bg-blue-900/20"
                  >
                    <Edit2 className="mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
                    {t("edit")}
                  </Button>
                  )}
                  {canDelete && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setIsDeleteModalOpen(true)}
                    className="border-red-200 text-red-600 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <Trash2 className="mr-1.5 h-3 w-3 sm:h-4 sm:w-4" />
                    {t("delete")}
                  </Button>
                  )}
                  {canExport && (
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={isLoadingEvents}
                    onClick={() => {
                      openPDFModal(
                        { contacts: [contact] },
                        `${t('contact_details')} - ${contact.name}`,
                        'contacts'
                      );
                    }}
                    className={`border-gray-200 text-gray-600 dark:border-gray-800 ${
                      isLoadingEvents
                        ? "cursor-not-allowed opacity-50"
                        : "dark:hover:bg-gray-900/20"
                    }`}
                    title={
                      isLoadingEvents
                        ? t("Loading events...")
                        : t("generatePDF")
                    }
                  >
                    <ArrowUpRight className="ml-1.5 h-3 w-3 sm:h-4 sm:w-4" />
                    {isLoadingEvents ? t("Loading...") : t("generatePDF")}
                  </Button>
                  )}
                </div>
              </div>
            </div>

            {/* Contact Info */}
            <section>
              {contact.email || contact.phone || contact.address ? (
                <h4 className="mb-4 text-base font-medium text-gray-900 dark:text-white sm:text-lg">
                  {t("contactInformation")}
                </h4>
              ) : null}
              <div className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2 lg:grid-cols-3">
                {[
                  {
                    icon: <Mail className="text-blue-500" />,
                    label: "email",
                    value: contact.email,
                  },
                  {
                    icon: <Phone className="text-green-500" />,
                    label: "phone",
                    value: contact.phone,
                  },
                  {
                    icon: <MapPin className="text-red-500" />,
                    label: "address",
                    value: contact.address,
                  },
                ]
                  .filter((item) => item.value)
                  .map((item, idx) => (
                    <div
                      key={idx}
                      className="flex items-start gap-3 rounded-lg bg-gray-50 p-3 dark:bg-gray-800/30 sm:p-4"
                    >
                      {React.cloneElement(item.icon, {
                        size: 18,
                        className: `${item.icon.props.className} mt-1 sm:size-5`,
                      })}
                      <div className="min-w-0 flex-1">
                        <p className="text-sm font-medium text-gray-500 dark:text-gray-400 sm:text-base">
                          {t(item.label)}
                        </p>
                        <p className="mt-1 break-words text-sm text-gray-900 dark:text-gray-100 sm:text-base">
                          {item.value}
                        </p>
                      </div>
                    </div>
                  ))}
              </div>
            </section>

            {/* Locations */}
            <section className="grid grid-cols-1 gap-4 sm:gap-6 md:grid-cols-2">
              {contact.ownedLocations.length > 0 && (
                <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/30 sm:p-6">
                  <h4 className="mb-3 text-base font-medium text-gray-900 dark:text-white sm:mb-4 sm:text-lg">
                    {t("ownedLocations")}
                  </h4>
                  {renderLocations(contact.ownedLocations, "owned")}
                </div>
              )}
              {contact.sharedLocations.length > 0 && (
                <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/30 sm:p-6">
                  <h4 className="mb-3 text-base font-medium text-gray-900 dark:text-white sm:mb-4 sm:text-lg">
                    {t("sharedLocations")}
                  </h4>
                  {renderLocations(contact.sharedLocations, "shared")}
                </div>
              )}
            </section>
          </CardContent>
          {/* Reservations Section */}
          {contactReservations.length > 0 && (
            <CardContent className="space-y-6 p-4 pt-6 sm:space-y-8 sm:p-6">
              <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white sm:text-xl">
                  {t("reservations")}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowReservations((prev) => !prev)}
                  className="w-full border-gray-200 text-gray-600 dark:border-gray-800 dark:hover:bg-gray-900/20 sm:w-auto"
                >
                  {showReservations ? t("hide") : t("show")}
                </Button>
              </div>
              {showReservations && (
                <div className="overflow-hidden rounded-lg bg-gray-50 dark:bg-gray-800/30">
                  <div className="w-full overflow-x-auto">
                    <ReservationList
                      props_reservations={contactReservations}
                      onDeleteReservation={(id: number) => {
                        setContactReservations((prev) =>
                          prev.filter((res) => res.id !== String(id)),
                        );
                      }}
                      embedded={true}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          )}

          {/* Financial Data Section */}
          {contactEvents.length > 0 && (
            <CardContent className="space-y-6 p-4 pt-6 sm:space-y-8 sm:p-6">
              <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-0">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white sm:text-xl">
                  {t("financialData")}
                </h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowFinances((prev) => !prev)}
                  className="w-full border-gray-200 text-gray-600 dark:border-gray-800 dark:hover:bg-gray-900/20 sm:w-auto"
                >
                  {showFinances ? t("hide") : t("show")}
                </Button>
              </div>

              {showFinances && (
                <div className="rounded-lg bg-gray-50 p-4 dark:bg-gray-800/30 sm:p-6">
                  {/* Tabs for Incomes, Expenses, and All Events */}
                  <div className="mb-6 flex flex-col border-b border-gray-200 dark:border-gray-700 sm:flex-row">
                    <button
                      onClick={() => setActiveTab("all")}
                      className={`px-3 py-2 text-sm font-medium transition-colors sm:px-4 ${
                        activeTab === "all"
                          ? "border-b-2 border-blue-500 text-blue-600"
                          : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      }`}
                    >
                      {t("allEvents")}
                    </button>
                    <button
                      onClick={() => setActiveTab("incomes")}
                      className={`px-3 py-2 text-sm font-medium transition-colors sm:px-4 ${
                        activeTab === "incomes"
                          ? "border-b-2 border-blue-500 text-blue-600"
                          : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      }`}
                    >
                      {t("incomes")}
                    </button>
                    <button
                      onClick={() => setActiveTab("expenses")}
                      className={`px-3 py-2 text-sm font-medium transition-colors sm:px-4 ${
                        activeTab === "expenses"
                          ? "border-b-2 border-blue-500 text-blue-600"
                          : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                      }`}
                    >
                      {t("expenses")}
                    </button>
                  </div>

                  {/* Upcoming Events Page */}
                  {contactEvents.length > 0 && (
                    <UpcomingEventsPage
                      events={contactEvents}
                      setEvents={setContactEvents}
                      mode={
                        activeTab === "incomes"
                          ? "income"
                          : activeTab === "expenses"
                            ? "expenses"
                            : "overview"
                      }
                      title=" "
                    />
                  )}
                </div>
              )}
            </CardContent>
          )}
        </Card>

        {/* Location Modal */}
        {locationModalType && (
          <LocationModal
            locations={(locationModalType === "owned"
              ? contact.ownedLocations
              : contact.sharedLocations
            ).map((l) => `${l.name} - ${l.address}`)}
            title={
              locationModalType === "owned"
                ? t("ownedLocations")
                : t("sharedLocations")
            }
            onClose={() => setLocationModalType(null)}
          />
        )}

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setDeleteError(null);
          }}
          onConfirm={handleDelete}
          message={`${t("deleteContactWarning")}`}
          conditions={[
            t("deleteContactConditionsNote"),
            t("deleteContactConditionsOwnership"),
            t("deleteContactConditionsIncomeEvents"),
            t("deleteContactConditionsExpenseEvents"),
            t("deleteContactConditionsContractsReservations"),
          ]}
          title={t("deleteContact")}
          itemName={contact?.name || ""}
          isLoading={isDeleting}
          error={deleteError}
        />

        {/* PDF Generator Modal */}
        <PDFGeneratorModal
          isOpen={modalProps.isOpen}
          onClose={modalProps.onClose}
          data={modalProps.data}
          title={modalProps.title}
          mode={modalProps.mode}
        />
      </div>
    </>
  );
};

export default ContactDetails;
