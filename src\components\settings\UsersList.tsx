"use client";
import React, { useState, useEffect } from "react";
import { UserDetails, UserPermissions } from "@/lib/interfaces/userDetails";
import { Search, ChevronDown, ChevronUp, PlusCircle, Edit2, Trash2, EyeIcon, ArrowLeft, Mail, Calendar } from "lucide-react";
import Image from "next/image";
import UserDetailsPanel from "./UserDetailsPanel";
import UserForm from "./UserForm";
import useLanguage from "@/hooks/useLanguage";
import DeleteConfirmationModal from "../Modals/DeleteConfirmationModal";
import userServices from "@/lib/users";
import { UpdateUserPayload } from "@/lib/users";
import { useUserServices } from "@/hooks/useUserService";
import { ErrorPopup } from "@/components/common/errorPopUp";
import { SuccessPopup } from "@/components/common/successPopUp";
import { LoadingComp } from "@/components/common/Loading";
import { set } from "date-fns";

type SortField = keyof Pick<UserDetails, "username" | "email" | "created_at"> | "name";

const UsersList: React.FC = () => {
  const { t } = useLanguage();
  const userServices = useUserServices();
  const [users, setUsers] = useState<UserDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortField, setSortField] = useState<SortField>("username");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [selectedUser, setSelectedUser] = useState<UserDetails | null>(null);
  const [isUserFormOpen, setIsUserFormOpen] = useState(false);
  const [userToEdit, setUserToEdit] = useState<UserDetails | undefined>(undefined);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<UserDetails | null>(null);
  const [viewMode, setViewMode] = useState<'list' | 'details' | 'add'>('list');
  const [isCardView, setIsCardView] = useState(typeof window !== 'undefined' ? window.innerWidth < 768 : false);
  const [submitting, setSubmitting] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const apiUsers = await userServices.listUsers();
        const transformedUsers = apiUsers.map(apiUser => userServices.transformToUserDetails(apiUser));
        setUsers(transformedUsers);
        setError(null);
      } catch (err) {
        console.error("Error fetching users:", err);
        setError(t("errorLoadingUsers") || "Failed to load users");
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSubmitUser = async (userData: Partial<UserDetails>) => {
    try {
      console.log("Submitting user data:", userData);
      
      setSubmitting(true);
      if (userToEdit) {
        // Construct the payload in the required format
        const updatePayload: UpdateUserPayload = {
          user: {
            email: userData.email,
            username: userData.username,
            role: userData.role,
            first_name: userData.first_name,
            last_name: userData.last_name,
            is_active: true, // You can make this dynamic based on your needs
            ...(userData.password && {
              password: userData.password,
              password_confirm: userData.password
            })
          },
          permissions: userData.permissions || {}
        };

        const updatedUser = await userServices.updateUserPermissions(
          userToEdit.id,
          updatePayload
        );
        const updatedUsers = users.map(user => 
          user.id === userToEdit.id ? userServices.transformToUserDetails(updatedUser) : user
        );
        setUsers(updatedUsers);
        
        if (selectedUser && selectedUser.id === userToEdit.id) {
          setSelectedUser(userServices.transformToUserDetails(updatedUser));
        }
        
        setIsUserFormOpen(false);
        
        setSuccessMessage(t("userUpdatedSuccess") || "User updated successfully");
        // refresh the page 
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else {
        const newUser = await userServices.createUser({
          email: userData.email || '',
          password: userData.password || '',
          password_confirm: userData.password || '',
          role: userData.role || 'USER',
          first_name: userData.first_name,
          last_name: userData.last_name,
          username: userData.username
        });
        
        setUsers(prev => [...prev, userServices.transformToUserDetails(newUser)]);
        setViewMode('list');
        setIsUserFormOpen(false);
        setUserToEdit(undefined);
        setSuccessMessage(t("userCreatedSuccess") || "User created successfully");
        // refresh the page
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error("Error saving user:", error);
      setErrorMessage(t("errorSavingUser") || "Failed to save user");
    } finally {
      setSubmitting(false);
    }
  };

  useEffect(() => {
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const apiUsers = await userServices.listUsers();
        
        const transformedUsers = apiUsers.map(apiUser => userServices.transformToUserDetails(apiUser));
        console.log("Fetched and transformed users:", transformedUsers);
        
        setUsers(transformedUsers);
        setError(null);
      } catch (err) {
        console.error("Error fetching users:", err);
        setError(t("errorLoadingUsers") || "Failed to load users");
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  useEffect(() => {
    if (typeof window === 'undefined') return;
    
    const handleResize = () => {
      setIsCardView(window.innerWidth < 768);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const handleAddUser = () => {
    setUserToEdit(undefined);
    setViewMode('add');
  };

  const handleEditUser = (user: UserDetails) => {
    setUserToEdit(user);
    setIsUserFormOpen(true);
  };

  const handleDeleteUser = (user: UserDetails) => {
    setUserToDelete(user);
    setIsDeleteModalOpen(true);
  };


  const handleUserClick = (user: UserDetails) => {
    setSelectedUser(user);
    setViewMode('details');
  };

  const handleCloseDetails = () => {
    setSelectedUser(null);
    setViewMode('list');
  };

  const handleCancelAddUser = () => {
    setViewMode('list');
  };

  const sortedUsers = [...users].sort((a, b) => {
    if (sortField === "created_at") {
      const dateA = new Date(a[sortField]);
      const dateB = new Date(b[sortField]);
      return sortDirection === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
    } else if (sortField === "name") {
      const nameA = `${a.first_name} ${a.last_name}`.toLowerCase();
      const nameB = `${b.first_name} ${b.last_name}`.toLowerCase();
      return sortDirection === "asc" ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
    } else {
      const valueA = a[sortField]?.toString().toLowerCase() || '';
      const valueB = b[sortField]?.toString().toLowerCase() || '';
      return sortDirection === "asc" ? valueA.localeCompare(valueB) : valueB.localeCompare(valueA);
    }
  });

  const filteredUsers = sortedUsers.filter(
    (user) =>
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getSortIcon = (field: SortField) => {
    if (sortField !== field) return null;
    return sortDirection === "asc" ? <ChevronUp size={16} /> : <ChevronDown size={16} />;
  };

  // Helper function to count active permissions
  const countActivePermissions = (user: UserDetails) => {
    if (!user.permissions) return 0;
    
    let count = 0;
    for (const module of Object.values(user.permissions)) {
      for (const value of Object.values(module)) {
        if (value === true) count++;
      }
    }
    return count;
  };

  if (loading) {
    return (
      <div className="bg-white shadow-xl rounded-2xl p-8 text-center">
        <p>{t("loadingUsers") || "Loading users..."}</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white shadow-xl rounded-2xl p-8 text-center text-red-500">
        <p>{error}</p>
        <button 
          onClick={() => setError(null)}
          className="mt-4 px-4 py-2 bg-primary text-white rounded-lg"
        >
          {t("tryAgain") || "Try Again"}
        </button>
      </div>
    );
  }
  if(submitting || deleting) {
    return (
      <div className="bg-white shadow-xl rounded-2xl p-8 text-center">
        <LoadingComp />
      </div>
    );
  }

  return (
    <div className="bg-white shadow-xl rounded-2xl overflow-hidden">

      
      {successMessage && (
        <SuccessPopup 
          message={successMessage} 
          onClose={() => setSuccessMessage(null)}
        />
      )}
      
      {errorMessage && (
        <ErrorPopup
          message={errorMessage}
          onClose={() => setErrorMessage(null)}
        />
      )}
      {/* Header */}
      <div className="p-4 sm:p-6 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0">
          <div>
            <h3 className="text-xl sm:text-2xl font-semibold text-gray-800">
              {t("userManagement") || "User Management"}
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              {t("manageUsersDescription") || "Manage system users and their permissions"}
            </p>
          </div>
          
          {viewMode === 'list' && (
            <button
              onClick={handleAddUser}
              className="flex items-center bg-primary text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg hover:bg-primary/90 transition mt-2 sm:mt-0"
            >
              <PlusCircle size={18} className="mr-1 sm:mr-2" />
              {t("addNewUser") || "Add New User"}
            </button>
          )}
          
          {viewMode === 'add' && (
            <button
              onClick={handleCancelAddUser}
              className="flex items-center bg-gray-100 text-gray-700 px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg hover:bg-gray-200 transition mt-2 sm:mt-0"
            >
              <ArrowLeft size={18} className="mr-1 sm:mr-2" />
              {t("backToUsers") || "Back to Users"}
            </button>
          )}
        </div>
      </div>

      {/* View Modes */}
      {viewMode === 'details' && selectedUser && (
        <UserDetailsPanel 
          user={selectedUser} 
          onClose={handleCloseDetails} 
          onEdit={() => handleEditUser(selectedUser)}
          onDelete={() => handleDeleteUser(selectedUser)}
        />
      )}
      
      {viewMode === 'add' && (
        <div className="p-4 sm:p-6">
          <UserForm 
            isOpen={true}
            onClose={handleCancelAddUser}
            onSubmit={handleSubmitUser}
            view="inline"
          />
        </div>
      )}
      
      {viewMode === 'list' && (
        <>
          {/* Search */}
          <div className="p-4 sm:p-6 border-b border-gray-200 bg-gray-50">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder={t("searchUsers") || "Search users..."}
                className="w-full pl-10 pr-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/30 focus:outline-none"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Users List */}
          {isCardView ? (
            /* Mobile Card View */
            <div className="p-4 space-y-4">
              {filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <div 
                    key={user.id}
                    onClick={() => handleUserClick(user)}
                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md cursor-pointer"
                  >
                    <div className="flex items-center gap-3">

                      <div className="flex-grow min-w-0">
                 
                        <p className="text-xs text-gray-500 mt-1">
                          {countActivePermissions(user)} active permissions
                        </p>
                      </div>
                    </div>
                    
                    <div className="mt-3 grid grid-cols-2 gap-2 text-xs text-gray-500">
                      <div className="flex items-center">
                        <Mail size={14} className="mr-1" />
                        <span className="truncate">{user.email}</span>
                      </div>
                      <div className="flex items-center">
                        <Calendar size={14} className="mr-1" />
                        <span>{new Date(user.created_at).toLocaleDateString()}</span>
                      </div>
                    </div>
                    
                    <div className="mt-3 pt-2 border-t border-gray-100 flex justify-end space-x-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditUser(user);
                        }}
                        className="text-amber-600 p-1"
                      >
                        <Edit2 size={16} />
                      </button>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-500">
                  {t("noUsersFound") || "No users found"}
                </div>
              )}
            </div>
          ) : (
            /* Desktop Table View */
            <div className="overflow-x-auto">
              <table className="w-full text-left">
                <thead className="bg-gray-50">
                  <tr>

                    <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("email")}>
                        {t("email") || "Email"} {getSortIcon("email")}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <div className="flex items-center cursor-pointer" onClick={() => handleSort("created_at")}>
                        {t("joined") || "Joined"} {getSortIcon("created_at")}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t("permissions") || "Permissions"}
                    </th>
                    <th className="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                      {t("actions") || "Actions"}
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredUsers.length > 0 ? (
                    filteredUsers.map((user) => (
                      <tr 
                        key={user.id} 
                        className="hover:bg-gray-50"
                      >

         
                        <td 
                          className="px-6 py-4 whitespace-nowrap cursor-pointer"
                          onClick={() => handleUserClick(user)}
                        >
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </td>
                        <td 
                          className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 cursor-pointer"
                          onClick={() => handleUserClick(user)}
                        >
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td
                          className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 cursor-pointer"
                          onClick={() => handleUserClick(user)}
                        >
                          {countActivePermissions(user)} active
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm">
                          <div className="flex space-x-2 justify-end">
                            <button
                              onClick={() => handleUserClick(user)}
                              className="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50"
                              title={t("viewDetails") || "View details"}
                            >
                              <EyeIcon size={18} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleEditUser(user);
                              }}
                              className="text-amber-600 hover:text-amber-800 px-2 py-1 rounded hover:bg-amber-50"
                              title={t("editUser") || "Edit user"}
                            >
                              <Edit2 size={18} />
                            </button>
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteUser(user);
                              }}
                              className="text-red-600 hover:text-red-800 px-2 py-1 rounded hover:bg-red-50"
                              title={t("deleteUser") || "Delete user"}
                            >
                              <Trash2 size={18} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={6} className="px-6 py-4 text-center text-gray-500">
                        {t("noUsersFound") || "No users found"}
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}

          {/* Footer */}
          <div className="bg-gray-50 px-4 sm:px-6 py-3 sm:py-4 border-t border-gray-200">
            <p className="text-sm text-gray-500">
              {t("showing") || "Showing"} <span className="font-medium">{filteredUsers.length}</span> {t("of") || "of"}{" "}
              <span className="font-medium">{users.length}</span> {t("users") || "users"}
            </p>
          </div>
        </>
      )}

      {/* Modals */}
      <UserForm
        isOpen={isUserFormOpen}
        onClose={() => setIsUserFormOpen(false)}
        user={userToEdit}
        onSubmit={handleSubmitUser}
        view="modal"
      />

      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={()=> {}}
        title={t("deleteUser") || "Delete User"}
        itemName={userToDelete?.first_name + ' ' + userToDelete?.last_name || ''}
        message={t("deleteUserConfirmation") || "Are you sure you want to delete this user?"}
      />
    </div>
  );
};

export default UsersList;