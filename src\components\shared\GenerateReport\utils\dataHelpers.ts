import { PDFData } from "../types";
import { containsArabic } from "./formatters";

/**
 * Check if any data contains Arabic text
 */
export const dataContainsArabic = (data: PDFData): boolean => {
  // Check events
  if (data.events) {
    for (const event of data.events) {
      if (containsArabic(event.title) ||
          containsArabic(event.description || '') ||
          containsArabic(event.contact?.name || '') ||
          containsArabic(event.location?.name || '')) {
        return true;
      }
    }
  }

  // Check locations
  if (data.locations) {
    for (const location of data.locations) {
      if (containsArabic(location.name) || containsArabic(location.address || '')) {
        return true;
      }
    }
  }

  // Check reservations
  if (data.reservations) {
    for (const reservation of data.reservations) {
      if (containsArabic(reservation.title) || containsArabic(reservation.description || '')) {
        return true;
      }
    }
  }

  // Check contracts
  if (data.contracts) {
    for (const contract of data.contracts) {
      if (containsArabic(contract.title) || containsArabic(contract.description || '')) {
        return true;
      }
    }
  }

  return false;
};
