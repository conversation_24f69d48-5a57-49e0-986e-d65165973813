import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { Location } from "@/lib/types/location";
import { FontInfo, PDFField, PDFData } from "../types";
import { getTranslation } from "../utils/translations";
import { formatMixedContent } from "../utils/formatters";
import { getFallbackFont, getAutoTableFont } from "../utils/fontHelpers";

/**
 * Generate Locations Table
 */
export const generateLocationsTable = async (
  doc: jsPDF,
  locations: Location[],
  language: string,
  startY: number,
  pageWidth: number,
  fields: PDFField[],
  data?: PDFData,
  fontInfo?: FontInfo
): Promise<number> => {
  let currentY = startY;

  // Use the comprehensive translation function
  const t = (key: string) => getTranslation(key, language);

  // Apply field edits if provided
  let processedLocations = [...locations];
  if (fields && fields.length > 0) {
    processedLocations = locations.map((location, index) => {
      const locationFields = fields.filter(f => f.key.startsWith(`location_${index}_`));
      let updatedLocation = { ...location };

      locationFields.forEach(field => {
        if (field.key.endsWith('_name')) updatedLocation.name = field.value;
        if (field.key.endsWith('_address')) updatedLocation.address = field.value;
        if (field.key.endsWith('_capacity')) updatedLocation.capacity = Number(field.value);
      });

      return updatedLocation;
    });
  }

  // Table columns with proper translations
  const tableColumn = language === "ar"
    ? [t("name"), t("address"), t("capacity"), t("status")]
    : [t("name"), t("address"), t("capacity"), t("status")];

  // Process locations with MIXED CONTENT SUPPORT
  const locationRows = processedLocations.map(location => [
    formatMixedContent(location.name || "-", language, 'text'), // Preserve original name
    formatMixedContent(location.address || "-", language, 'text'), // Preserve original address
    location.capacity?.toString() || "-", // Numbers don't need special formatting
    (location as any).is_active ? t("active") : t("inactive") // Translate status
  ]);

  // Improved title rendering
  doc.setFontSize(14);
  const locationsTitle = t("locations");
  const titleWidth = doc.getTextWidth(locationsTitle);
  doc.text(locationsTitle, language === "ar" ? pageWidth - titleWidth - 14 : 14, currentY);
  currentY += 8; // Reduced spacing to keep title close to table

  autoTable(doc, {
    head: [tableColumn],
    body: locationRows,
    startY: currentY + 5,
    styles: {
      halign: language === "ar" ? "right" : "left",
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontSize: 10,
      cellPadding: 4,
      lineColor: [200, 200, 200],
      lineWidth: 0.1,
    },
    headStyles: {
      font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
      fontStyle: 'bold', // Always use bold for headers
      halign: language === "ar" ? "right" : "left",
      fillColor: [46, 204, 113], // EXACT original green
      textColor: [255, 255, 255],
      fontSize: 11,
    },
    alternateRowStyles: {
      fillColor: [248, 249, 250],
    },
    columnStyles: {
      0: { halign: language === "ar" ? "right" : "left" },
      1: { halign: language === "ar" ? "right" : "left" },
      2: { halign: language === "ar" ? "right" : "center" },
      3: { halign: language === "ar" ? "right" : "center" }
    }
  });

  currentY = (doc as any).lastAutoTable.finalY + 20;

  return currentY;
};
