import { EventDetails } from "@/lib/interfaces/finaces";
import { Contact } from "@/lib/types/contacts";
import { Location } from "@/lib/types/location";
import { Reservation } from "@/lib/interfaces/reservation";
import { Contract } from "@/lib/interfaces/contract";

export interface PDFField {
  key: string;
  label: string;
  value: string;
  visible: boolean;
  editable: boolean;
  type: 'text' | 'number' | 'date' | 'currency' | 'textarea';
  category: string;
}

export interface PDFOptions {
  language: 'ar' | 'en';
  includeCompanyInfo: boolean;
  includeLogo: boolean;
  includeDate: boolean;
  customTitle: string;
  fields: PDFField[];
}

export interface PDFData {
  events?: EventDetails[];
  contacts?: Contact[];
  locations?: Location[];
  reservations?: Reservation[];
  contracts?: Contract[];
}

export type PDFMode = 'events' | 'contacts' | 'locations' | 'reservations' | 'contracts';

export interface FontInfo {
  fontName: string;
  hasBold: boolean;
}

export interface PDFGeneratorModalProps {
  isOpen: boolean;
  onClose: () => void;
  data: PDFData;
  title: string;
  mode: PDFMode;
}

export interface RenderTextOptions {
  align?: 'left' | 'right' | 'center';
  language?: string;
  fontSize?: number;
  fontName?: string;
}
