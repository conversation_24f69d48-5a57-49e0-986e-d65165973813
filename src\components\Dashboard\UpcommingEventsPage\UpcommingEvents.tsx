"use client";

import React, { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaFilePdf } from "react-icons/fa";
import EventCard from "../../Financials/common/EventCard";
import useLanguage from "@/hooks/useLanguage";
import { EventDetails } from "@/lib/interfaces/finaces";
import FilterArea from "./FilterArea";
import SelectedEventDetails from "./SelectedEventDetails";
import AddEventPopup from "../../Financials/common/AddEventPopup.tsx/AddEventPopup";
import { PDFGeneratorModal } from "@/components/shared/GenerateReport";
import { usePermissions } from "@/hooks/usePermissions";
import { LoadingComp } from "@/components/common/Loading";
import EventWithChildrenCard from "@/components/Financials/common/EventWithChildrenCard";
import IncomeServices from "@/lib/income";
import IncomeEditForm from "../../Financials/Income/IncomeEditForm";
import ExpenseEditForm from "../../Financials/Expenses/ExpenseEditForm";
import { canEditIncome } from "@/types/income";
import { canEditExpense } from "@/types/expenses";
import { toast } from "react-hot-toast";
import ExpenseServices from "@/lib/expenses";
import { usePDFGenerator } from "@/hooks/usePDFGenerator";

interface UpcomingEventsPageProps {
  events: EventDetails[];
  setEvents: React.Dispatch<React.SetStateAction<EventDetails[]>>;
  mode?: "overview" | "income" | "expenses";
  selectedEventId?: string; // Changed from number to string to match event.id type
  title?: string; // Optional title prop
}

const UpcomingEventsPage: React.FC<UpcomingEventsPageProps> = ({
  events,
  setEvents,
  mode = "overview",
  selectedEventId,
  title,
}) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [filterType, setFilterType] = useState<"All" | "income" | "expense">(
    "All",
  );

  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [filterContact, setFilterContact] = useState("all");
  const [filterLocation, setFilterLocation] = useState("all");
  const [filterPriceFrom, setFilterPriceFrom] = useState("");
  const [filterPriceTo, setFilterPriceTo] = useState("");
  const [selectedEvent, setSelectedEvent] = useState<EventDetails | null>(null);
  const [isAddEventPopupOpen, setIsAddEventPopupOpen] = useState(false);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<EventDetails | null>(null);
  const [showIncomeEditForm, setShowIncomeEditForm] = useState(false);
  const [showExpenseEditForm, setShowExpenseEditForm] = useState(false);
  const { t, language } = useLanguage();
  const infoSectionRef = useRef<HTMLDivElement>(null);
  const topRef = useRef<HTMLDivElement>(null);
  const { hasPermission, permissionsLoaded } = usePermissions();
  const incomeService = IncomeServices();
  const expenseService = ExpenseServices();
  const { modalProps, openPDFModal, closePDFModal } = usePDFGenerator();
  const [createdAtStart, setCreatedAtStart] = useState("");
  const [createdAtEnd, setCreatedAtEnd] = useState("");
  const [filterStatus, setFilterStatus] = useState("All");
  const [filterStatusOptions, setFilterStatusOptions] = useState<string[]>([
    "All",
  ]);

  useEffect(() => {
    if (selectedEventId) {
      const eventToSelect = events.find(
        (event) => event.id.toString() === selectedEventId,
      );
      if (eventToSelect) {
        setSelectedEvent(eventToSelect);
        scrollToInfoSection();
      }
    }
  }, [selectedEventId, events]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) =>
    setSearchQuery(e.target.value);

  const handleEdit = (id: number | string) => {
    // Convert id to string to ensure consistent comparison
    const eventToEdit = events.find(
      (event) => event.id.toString() === id.toString(),
    );
    if (eventToEdit) {
      // Always just show details when clicking on card
      // Edit functionality is only available through the edit button in details
      setSelectedEvent(eventToEdit);
      scrollToInfoSection();
    }
  };

  // New function to handle opening edit forms (called from details view)
  const handleOpenEditForm = (event: EventDetails) => {
    const canEdit =
      event.category === "income"
        ? canEditIncome(event)
        : canEditExpense(event);

    if (!canEdit) {
      const linkType = event.reservation_id ? "reservation" : "contract";
      toast.error(t(`Cannot edit: linked to ${linkType}`));
      return;
    }

    setEditingEvent(event);
    if (event.category === "income") {
      setShowIncomeEditForm(true);
    } else {
      setShowExpenseEditForm(true);
    }
  };
  const generateDescriptiveTitle = () => {
    let title =
      mode === "income"
        ? t("Income Report")
        : mode === "expenses"
          ? t("Expense Report")
          : t("Financial Report");

    if (filterContact !== "all") {
      title += ` ${t("for contact")}: "${filterContact}"`;
    }

    if (filterLocation !== "all") {
      title += ` ${t("at location")}: "${filterLocation}"`;
    }

    // Always include the date range
    if (startDate && endDate) {
      title += ` ${t("from")} ${new Date(startDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")} ${t("to")} ${new Date(endDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else if (startDate) {
      title += ` ${t("starting from")} ${new Date(startDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else if (endDate) {
      title += ` ${t("up to")} ${new Date(endDate).toLocaleDateString(language === "ar" ? "ar-EG" : "en-US")}`;
    } else {
      title += ` ${t("as of today")}`;
    }

    return title;
  };

  const handleDelete = (id: number | string) => {
    // Convert id to string for comparison
    setEvents(events.filter((event) => event.id.toString() !== id.toString()));
  };

  const handleHistory = (id: number | string) => {
    // Convert id to string for comparison
    const eventToViewHistory = events.find(
      (event) => event.id.toString() === id.toString(),
    );
    if (eventToViewHistory) {
      setSelectedEvent(eventToViewHistory);
    }
  };

  const handleSave = async (updatedEvent: EventDetails) => {
    try {
      if (updatedEvent.category === "income") {
        await incomeService.editIncome(updatedEvent.id, {
          title: updatedEvent.title,
          amount: updatedEvent.amount,
          due_date: updatedEvent.dueDate,
          received_date: updatedEvent.received_date || null,
          description: updatedEvent.description || "",
          status: updatedEvent.status,
          priority: updatedEvent.priority,
          type_id: updatedEvent.type || undefined,
        });
      } else if (updatedEvent.category === "expense") {
        await expenseService.editExpense(updatedEvent.id, {
          title: updatedEvent.title,
          amount: updatedEvent.amount,
          due_date: updatedEvent.dueDate,
          paid_date: updatedEvent.paid_date || null,
          description: updatedEvent.description || "",
          status: updatedEvent.status,
          priority: updatedEvent.priority,
          type_id: updatedEvent.type || undefined,
        });
      }

      setSelectedEvent(null);
    } catch (error: any) {
      console.error("Error saving event:", error);

      // Handle 409 conflict error (linked to reservation/contract)
      if (error.can_update === false) {
        if (error.reservation) {
          toast.error(
            t("Cannot edit: linked to reservation ") + error.reservation.title,
          );
        } else if (error.contract) {
          toast.error(
            t("Cannot edit: linked to contract ") + error.contract.title,
          );
        } else {
          toast.error(error.error || t("Cannot update this event"));
        }
      } else {
        // Re-throw other errors to be handled by the calling component
        toast.error(error.message || t("Failed to update event"));
        throw error;
      }
    }
  };

  const handleAddEventSave = (newEvents: EventDetails[]) => {
    setEvents((prevEvents) => [...prevEvents, ...newEvents]);
  };

  // Edit form handlers
  const handleIncomeEditSave = (updatedIncome: any) => {
    setEvents((prevEvents) =>
      prevEvents.map((event) =>
        event.id === updatedIncome.id ? { ...event, ...updatedIncome } : event,
      ),
    );
    setEditingEvent(null);
    setShowIncomeEditForm(false);
  };

  const handleExpenseEditSave = (updatedExpense: any) => {
    setEvents((prevEvents) =>
      prevEvents.map((event) =>
        event.id === updatedExpense.id
          ? { ...event, ...updatedExpense }
          : event,
      ),
    );
    setEditingEvent(null);
    setShowExpenseEditForm(false);
  };

  const handleEditFormClose = () => {
    setEditingEvent(null);
    setShowIncomeEditForm(false);
    setShowExpenseEditForm(false);
  };

  const scrollToInfoSection = () => {
    if (infoSectionRef.current) {
      infoSectionRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const scrollToTop = () => {
    if (topRef.current) {
      topRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const clearFilters = () => {
    setFilterType("All");
    setFilterStatus("All");
    setFilterContact("all");
    setFilterLocation("all");
    setFilterPriceFrom("");
    setFilterPriceTo("");
    setSearchQuery("");
  };
  const filteredEvents = events
    .filter((event) => {
      const matchesSearch =
        event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.contact?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        event.location?.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilterType =
        filterType === "All" || event.category === filterType;
      const matchesStatus =
        filterStatus === "All" || event.status === filterStatus;
      const matchesContact =
        filterContact === "all" || event.contact?.name === filterContact;
      const matchesLocation =
        filterLocation === "all" || event.location?.name === filterLocation;
      const matchesPriceFrom =
        filterPriceFrom === "" || event.amount >= parseFloat(filterPriceFrom);
      const matchesPriceTo =
        filterPriceTo === "" || event.amount <= parseFloat(filterPriceTo);

      return (
        matchesSearch &&
        matchesFilterType &&
        matchesStatus &&
        matchesContact &&
        matchesLocation &&
        matchesPriceFrom &&
        matchesPriceTo
      );
    })
    .filter((event) => {
      if (mode === "income") return event.category === "income";
      if (mode === "expenses") return event.category === "expense";
      return true;
    });

  // Update options based on filtered events
  useEffect(() => {
    const uniqueStatuses = Array.from(
      new Set(filteredEvents.map((event) => event.status)),
    );
    setFilterStatusOptions((prevOptions) => {
      const newOptions = ["All", ...uniqueStatuses];
      if (JSON.stringify(prevOptions) !== JSON.stringify(newOptions)) {
        return newOptions;
      }
      return prevOptions;
    });
  }, [filteredEvents]);

  useEffect(() => {
    if (!filterStatusOptions.includes(filterStatus)) {
      setFilterStatus("All");
    }
  }, [filterStatusOptions, filterStatus]);

  if (!permissionsLoaded) {
    return <LoadingComp />;
  }
  return (
    <div className="min-h-screen p-2 sm:p-4 lg:p-6 xl:p-8">
      <div ref={topRef}></div>
      {/* Header */}
      <div className="mb-4 sm:mb-6">
        {/* Title */}
        <div className="mb-4 sm:mb-6">
          <h1 className="text-xl font-bold text-gray-800 dark:text-gray-100 sm:text-2xl lg:text-3xl xl:text-4xl">
            {title ||
              (mode === "overview"
                ? t("upcoming events")
                : mode === "income"
                  ? t("income events")
                  : mode === "expenses"
                    ? t("expense events")
                    : "")}
          </h1>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col gap-3 sm:flex-row sm:flex-wrap sm:items-center sm:justify-between">
          {/* Primary Actions */}
          <div className="flex flex-col gap-2 sm:flex-row sm:gap-3">
            <button
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-gray-500 px-3 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-gray-600 dark:bg-gray-700 dark:hover:bg-gray-800 sm:w-auto sm:px-4 sm:text-base"
            >
              <FaFilter className="h-4 w-4" />
              <span className="sm:inline">{t("filter")}</span>
            </button>

            <button
              onClick={() => {
                const title = generateDescriptiveTitle();
                const pdfMode = mode === "income" ? "events" : mode === "expenses" ? "events" : "events";
                openPDFModal(
                  { events: filteredEvents },
                  title,
                  pdfMode
                );
              }}
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-red-500 px-3 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-red-600 dark:bg-red-700 dark:hover:bg-red-800 sm:w-auto sm:px-4 sm:text-base"
            >
              <FaFilePdf className="h-4 w-4" />
              <span className="sm:inline">{t("generate pdf")}</span>
            </button>
          </div>

          {/* Add Event Button */}
          {(mode === "income" && hasPermission("income", "create")) ||
          (mode === "expenses" && hasPermission("expenses", "create")) ||
          (mode === "overview" && hasPermission("financials", "create")) ? (
            <button
              className="flex w-full items-center justify-center gap-2 rounded-lg bg-green-500 px-3 py-2 text-sm font-medium text-white shadow-sm transition-colors hover:bg-green-600 dark:bg-green-700 dark:hover:bg-green-800 sm:w-auto sm:px-4 sm:text-base"
              onClick={() => {
                console.log("Add Event button clicked");
                setIsAddEventPopupOpen(true);
              }}
            >
              <FaPlus className="h-4 w-4" />
              <span className="sm:inline">{t("add event")}</span>
            </button>
          ) : null}
        </div>
      </div>

      {/* Selected Event Details */}
      {selectedEvent && (
        <div ref={infoSectionRef} className="mb-4 sm:mb-6">
          <SelectedEventDetails
            event={selectedEvent}
            onClose={() => setSelectedEvent(null)}
            onEdit={() => handleOpenEditForm(selectedEvent)}
            onDelete={() => handleDelete(selectedEvent.id)}
            onHistory={() => handleHistory(selectedEvent.id)}
            onSave={handleSave} // Pass the handleSave function to handle saving the edited event
          />
        </div>
      )}
      {/* Filter Area */}
      {isFilterOpen && (
        <div className="mb-4 sm:mb-6">
          <FilterArea
            filterType={filterType}
            setFilterType={setFilterType}
            filterStatusOptions={filterStatusOptions}
            filterStatus={filterStatus}
            setFilterStatus={(value: string | null) => {
              setFilterStatus(value || "All");
            }}
            searchQuery={searchQuery}
            handleSearch={handleSearch}
            clearFilters={clearFilters}
            filterContact={filterContact}
            setFilterContact={setFilterContact}
            filterLocation={filterLocation}
            setFilterLocation={setFilterLocation}
            filterPriceFrom={filterPriceFrom}
            setFilterPriceFrom={setFilterPriceFrom}
            filterPriceTo={filterPriceTo}
            setFilterPriceTo={setFilterPriceTo}
            contactOptions={events
              .filter((event) => event.contact?.name) // Filter out undefined names
              .map((event) => ({
                value: event.contact!.name,
                label: event.contact!.name,
              }))}
            locationOptions={events
              .filter((event) => event.location?.name) // Filter out undefined names
              .map((event) => ({
                value: event.location!.name,
                label: event.location!.name,
              }))}
          />
        </div>
      )}
      {(mode === "income" && hasPermission("income", "view")) ||
      (mode === "expenses" && hasPermission("expenses", "view")) ||
      (mode === "overview" && hasPermission("financials", "view")) ? (
        <div className="space-y-4 sm:space-y-6">
          {/* Table Header - Hidden on mobile, visible on larger screens */}
          <div className="hidden rounded-t-lg bg-gray-100 p-3 dark:bg-gray-700 md:grid md:grid-cols-6 md:gap-4 lg:p-4">
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("title")}
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("date")}
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("amount")}
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("status")}
            </span>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("priority")}
            </span>
            <span className="text-right text-xs font-medium text-gray-700 dark:text-gray-300 lg:text-sm">
              {t("actions")}
            </span>
          </div>

          {/* Event Cards Container */}
          <div
            id="events-content"
            className="space-y-2 sm:space-y-3 lg:space-y-4"
          >
            {filteredEvents.length === 0 ? (
              <div className="flex min-h-[200px] items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50">
                <div className="text-center">
                  <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"></div>
                  <p className="text-sm text-gray-600 dark:text-gray-300 sm:text-base">
                    {t("no events found")}
                  </p>
                  <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 sm:text-sm">
                    {t("Try adjusting your filters or add a new event")}
                  </p>
                </div>
              </div>
            ) : (
              filteredEvents.map((event) => {
                if (event.child_count && event.child_count > 0) {
                  return (
                    <div
                      key={event.id}
                      className="transition-all duration-200 hover:scale-[1.01]"
                    >
                      <EventWithChildrenCard
                        event={event}
                        onEdit={() => handleEdit(event.id)}
                        onDelete={() => handleDelete(event.id)}
                        onClick={() => {
                          handleEdit(event.id);
                          scrollToTop();
                        }}
                      />
                    </div>
                  );
                } else {
                  return (
                    <div
                      key={event.id}
                      className="transition-all duration-200 hover:scale-[1.01]"
                    >
                      <EventCard
                        event={event}
                        onEdit={() => handleEdit(event.id)}
                        onDelete={() => handleDelete(event.id)}
                        onClick={() => {
                          handleEdit(event.id);
                          scrollToTop();
                        }}
                      />
                    </div>
                  );
                }
              })
            )}
          </div>
        </div>
      ) : (
        <div className="flex min-h-[300px] items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50 dark:border-gray-600 dark:bg-gray-800/50 sm:min-h-[400px]">
          <div className="p-4 text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-200 dark:bg-gray-700">
              <svg
                className="h-8 w-8 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              {t("Access Restricted")}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 sm:text-base">
              {t("no permission to add events")}
            </p>
            <p className="mt-1 text-xs text-gray-500 dark:text-gray-400 sm:text-sm">
              {t("Contact your administrator for access")}
            </p>
          </div>
        </div>
      )}

      {/* Add Event Popup */}
      {isAddEventPopupOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <AddEventPopup
            onClose={() => setIsAddEventPopupOpen(false)}
            onSave={handleAddEventSave}
            defaultCategory={mode === "income" ? "income" : "expense"}
          />
        </div>
      )}

      {/* Income Edit Form */}
      {showIncomeEditForm && editingEvent && (
        <IncomeEditForm
          income={editingEvent}
          onClose={handleEditFormClose}
          onSave={handleIncomeEditSave}
        />
      )}

      {/* Expense Edit Form */}
      {showExpenseEditForm && editingEvent && (
        <ExpenseEditForm
          expense={editingEvent}
          onClose={handleEditFormClose}
          onSave={handleExpenseEditSave}
        />
      )}

      {/* PDF Generator Modal */}
      <PDFGeneratorModal
        isOpen={modalProps.isOpen}
        onClose={modalProps.onClose}
        data={modalProps.data}
        title={modalProps.title}
        mode={modalProps.mode}
      />
    </div>
  );
};

export default UpcomingEventsPage;
