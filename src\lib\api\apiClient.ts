import axios, {
  AxiosError,
  AxiosInstance,
  InternalAxiosRequestConfig
} from "axios";
import { authService } from "./auth";
import { getSession } from "next-auth/react";

const BASE_URL = "http://localhost:8000"; 
// const BASE_URL = "https://sarayvera-backend-production.up.railway.app/";
const MAX_RETRIES = 1;


const createApiClient = (updateSession?: (data: any) => Promise<void>): AxiosInstance => {
  const apiClient = axios.create({
    baseURL: BASE_URL,
    headers: { "Content-Type": "application/json" },
  });

  let isRefreshing = false;
  let refreshSubscribers: ((token: string) => void)[] = [];

  apiClient.interceptors.request.use(async (config) => {
    const session = await getSession();
    if (session?.accessToken) {
      config.headers.Authorization = `Bearer ${session.accessToken}`;
    }
    return config;
  });

  apiClient.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;
      
      if (error.response?.status === 401 && !originalRequest._retry) {
        if (isRefreshing) {
          return new Promise((resolve) => {
            refreshSubscribers.push((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              resolve(apiClient(originalRequest));
            });
          });
        }

        originalRequest._retry = true;
        isRefreshing = true;

        try {
          const session = await getSession();
          if (!session?.refreshToken) {
            throw new Error("No refresh token available");
          }

          const { accessToken, refreshToken } = await authService.refreshToken(session.refreshToken);
          
          // Update the session
          if (updateSession) {
            await updateSession({
              ...session,
              accessToken,
              refreshToken: refreshToken || session.refreshToken
            });
          }

          // Update the header and retry the request
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          
          // Process all queued requests
          refreshSubscribers.forEach((cb) => cb(accessToken));
          refreshSubscribers = [];
          
          return apiClient(originalRequest);
        } catch (refreshError) {
          refreshSubscribers = [];
          console.log("Refresh token error:", refreshError);
          
          // await authService.logout();
          return Promise.reject(refreshError);
        } finally {
          isRefreshing = false;
        }
      }

      return Promise.reject(error);
    }
  );

  return apiClient;
};

const apiClient = createApiClient();
export default apiClient;

export const createApiClientWithSessionUpdate = (update: (data: any) => Promise<void>) => {
  return createApiClient(update);
};