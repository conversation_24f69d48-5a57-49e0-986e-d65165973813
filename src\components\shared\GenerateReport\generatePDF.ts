import jsPDF from "jspdf";
import { PDFData, PDFOptions, PDFMode } from "./types";
import { loadLogo, loadPerfectFont, renderText } from "./utils/fontHelpers";
import { getTranslation } from "./utils/translations";
import { formatDateForPDF } from "./utils/formatters";
import { dataContainsArabic } from "./utils/dataHelpers";
import { generateEventsTable } from "./utils/eventsTable";
import { generateLocationsTable } from "./utils/locationsTable";
import { generateContactsTable } from "./utils/contactsTable";
import { generateReservationsTable } from "./utils/reservationsTable";
import { generateContractsTable } from "./utils/contractsTable";

/**
 * Main PDF generation function
 */
export const generateCustomPDF = async (
  data: PDFData,
  options: PDFOptions,
  mode: PDFMode
): Promise<void> => {
  const { language, includeCompanyInfo, includeLogo, includeDate, customTitle, fields } = options;

  // RESPECT USER'S LANGUAGE CHOICE: Use the selected language from the modal
  const effectiveLanguage = language;
  
  console.log('📋 PDF Generation Settings:', {
    selectedLanguage: language,
    effectiveLanguage,
    includeCompanyInfo,
    includeLogo,
    includeDate,
    customTitle,
    mode,
    dataKeys: Object.keys(data),
    eventCount: data.events?.length || 0
  });

  try {
    // Create PDF document
    const doc = new jsPDF();
    const pageWidth = doc.internal.pageSize.getWidth();
    let currentY = 20;

    // Load the PERFECT font for Arabic + English
    const fontInfo = await loadPerfectFont(doc);
    console.log('🔤 Font info loaded:', fontInfo);
    
    // Set the font explicitly
    try {
      doc.setFont(fontInfo.fontName, 'normal');
      console.log('✅ Font set successfully:', fontInfo.fontName);
    } catch (error) {
      console.error('❌ Error setting font:', error);
      doc.setFont('helvetica', 'normal');
      console.log('⚠️ Fallback to helvetica');
    }

    // Load and add company logo
    if (includeLogo) {
      try {
        const logoBase64 = await loadLogo('/images/logo/saray_vera_white.png');
        if (logoBase64) {
          const logoWidth = 40;
          const logoHeight = 20;
          const logoX = effectiveLanguage === "ar" ? pageWidth - logoWidth - 14 : 14;
          doc.addImage(`data:image/png;base64,${logoBase64}`, 'PNG', logoX, currentY, logoWidth, logoHeight);
        }
      } catch (logoError) {
        console.warn("Could not load logo, continuing without it:", logoError);
      }
    }

    // Company name and document info
    if (includeCompanyInfo) {
      const companyName = getTranslation("company_name", effectiveLanguage);
      const companyNameWidth = doc.getTextWidth(companyName);
      const companyNameX = effectiveLanguage === "ar" ? 14 : pageWidth - companyNameWidth - 14;
      renderText(doc, companyName, companyNameX, currentY + 8, {
        language: effectiveLanguage,
        fontSize: 16,
        fontName: fontInfo.fontName
      });
    }

    // Document creation date
    if (includeDate) {
      const currentDate = new Date();
      const formattedDate = formatDateForPDF(currentDate.toISOString(), effectiveLanguage);
      const createdLabel = getTranslation("created", effectiveLanguage);
      const dateText = effectiveLanguage === "ar"
        ? `${formattedDate} :${createdLabel}`
        : `${createdLabel}: ${formattedDate}`;
      const dateWidth = doc.getTextWidth(dateText);
      const dateX = effectiveLanguage === "ar" ? 14 : pageWidth - dateWidth - 14;
      renderText(doc, dateText, dateX, currentY + 18, {
        language: effectiveLanguage,
        fontSize: 10,
        fontName: fontInfo.fontName
      });
    }

    // Add a separator line (EXACT original)
    currentY += 30;
    doc.setLineWidth(0.5);
    doc.line(14, currentY, pageWidth - 14, currentY);
    currentY += 10;

    // Report Title - Professional styling
    const reportTitle = customTitle;
    const reportTitleWidth = doc.getTextWidth(reportTitle);
    const titleX = (pageWidth - reportTitleWidth) / 2; // Center the title
    renderText(doc, reportTitle, titleX, currentY, {
      align: 'center',
      language: effectiveLanguage,
      fontSize: 20,
      fontName: fontInfo.fontName
    });

    // Add underline to title (EXACT original)
    doc.setLineWidth(0.3);
    doc.line(titleX, currentY + 2, titleX + reportTitleWidth, currentY + 2);
    currentY += 15;

    // Reset font to the perfect font
    doc.setFont(fontInfo.fontName, 'normal');

    // Generate tables based on mode
    if (mode === 'events' && data.events) {
      await generateEventsTable(doc, data.events, effectiveLanguage, currentY, pageWidth, fields, fontInfo);
    } else if (mode === 'locations' && data.locations) {
      await generateLocationsTable(doc, data.locations, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'reservations' && data.reservations) {
      await generateReservationsTable(doc, data.reservations, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'contracts' && data.contracts) {
      await generateContractsTable(doc, data.contracts, effectiveLanguage, currentY, pageWidth, fields, data, fontInfo);
    } else if (mode === 'contacts' && data.contacts) {
      await generateContactsTable(doc, data.contacts, effectiveLanguage, currentY, pageWidth, fontInfo);
    }

    // Generate filename
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `${customTitle.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_${timestamp}.pdf`;

    // Save the PDF
    doc.save(filename);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};
