import jsPDF from "jspdf";
import autoTable from "jspdf-autotable";
import { EventDetails } from "@/lib/interfaces/finaces";
import { FontInfo, PDFField } from "../types";
import { getTranslation } from "../utils/translations";
import { formatArabicCurrency, formatDateForPDF, formatMixedContent } from "../utils/formatters";
import { getTableFont, renderText, getAutoTableFont } from "../utils/fontHelpers";

/**
 * Generate Events Table with field editing support
 */
export const generateEventsTable = async (
  doc: jsPDF,
  events: EventDetails[],
  language: string,
  startY: number,
  pageWidth: number,
  fields: PDFField[],
  fontInfo: FontInfo
): Promise<number> => {
  let currentY = startY;

  // Apply field edits if provided
  let processedEvents = [...events];
  if (fields && fields.length > 0) {
    processedEvents = events.map((event, index) => {
      const eventFields = fields.filter(f => f.key.startsWith(`event_${index}_`));
      let updatedEvent = { ...event };

      eventFields.forEach(field => {
        if (field.key.endsWith('_title')) updatedEvent.title = field.value;
        if (field.key.endsWith('_amount')) updatedEvent.amount = parseFloat(field.value) || 0;
        if (field.key.endsWith('_dueDate')) updatedEvent.dueDate = field.value;
        if (field.key.endsWith('_status')) updatedEvent.status = field.value as any;
        if (field.key.endsWith('_priority')) updatedEvent.priority = field.value as any;
        if (field.key.endsWith('_description')) updatedEvent.description = field.value;
      });

      return updatedEvent;
    });
  }

  // Use the comprehensive translation function
  const t = (key: string) => {
    const translation = getTranslation(key, language);
    console.log(`🌐 Translation: "${key}" -> "${translation}" (${language})`);
    return translation;
  };

  // Table columns with proper translations
  const tableColumn = language === "ar"
    ? [t("title"), t("dueDate"), t("amount"), t("status")]
    : [t("title"), t("dueDate"), t("amount"), t("status")];
  
  console.log('📊 Table columns:', tableColumn, 'Language:', language);
  console.log('🔤 Font info for table:', fontInfo);

  const incomeRows: any[] = [];
  const expenseRows: any[] = [];

  // Process events with MIXED CONTENT SUPPORT
  processedEvents.forEach(event => {
    console.log('📝 Processing event:', event.title, 'Category:', event.category);
    // Use consistent date formatting for both languages (dd/mm/yyyy)
    const formattedDate = formatDateForPDF(event.dueDate, language);
    
    // Use smart currency formatting
    const formattedAmount = formatMixedContent(
      (Math.round(Number(event.amount) || 0)).toString(), 
      language, 
      'currency'
    );

    const eventData = [
      formatMixedContent(event.title, language, 'text'), // Preserve original title
      formattedDate,
      formattedAmount,
      t(event.status) // Translate status
    ];
    
    console.log('📊 Event data row:', eventData, 'Original title:', event.title);
    
    if (event.category === "income") incomeRows.push(eventData);
    else if (event.category === "expense") expenseRows.push(eventData);
  });

  // EXACT original totals calculation
  const totalIncome = Math.round(processedEvents
    .filter(e => e.category === "income")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalExpenses = Math.round(processedEvents
    .filter(e => e.category === "expense")
    .reduce((sum, e) => sum + (Number(e.amount) || 0), 0));

  const totalIncomeText = language === "ar"
    ? `${formatArabicCurrency(totalIncome)} :${t("total income")}`
    : `${t("total income")}: ${totalIncome}`;
  const totalExpensesText = language === "ar"
    ? `${formatArabicCurrency(totalExpenses)} :${t("total expenses")}`
    : `${t("total expenses")}: ${totalExpenses}`;

  // Income Table
  if (incomeRows.length > 0) {
    console.log('💰 Generating income table with', incomeRows.length, 'rows');
    console.log('💰 Income rows data:', incomeRows);
    
    const incomeTitle = t("incomeEvents");
    
    // Set font size first to get accurate width measurement
    doc.setFontSize(14);
    const titleWidth = doc.getTextWidth(incomeTitle);
    const titleX = language === "ar" ? pageWidth - titleWidth - 14 : 14;
    
    // Render title with proper font settings
    renderText(doc, incomeTitle, titleX, currentY, {
      language,
      fontSize: 14,
      fontName: fontInfo.fontName
    });
    currentY += 8; // Reduced spacing to keep title close to table

    autoTable(doc, {
      head: [tableColumn],
      body: incomeRows,
      startY: currentY + 3, // Reduced gap between title and table
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
        overflow: 'linebreak', // Handle long text properly
        cellWidth: 'wrap' // Allow cells to wrap content
      },
      headStyles: {
        font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
        fontStyle: 'bold', // Always use bold for headers
        halign: language === "ar" ? "right" : "left",
        fillColor: [41, 128, 185], // EXACT original blue
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left", cellWidth: 'auto' }, // Title column - auto width
        1: { halign: language === "ar" ? "right" : "center", cellWidth: 30 }, // Date column - fixed width
        2: { halign: language === "ar" ? "right" : "right", cellWidth: 25 }, // Amount column - fixed width
        3: { halign: language === "ar" ? "right" : "center", cellWidth: 25 } // Status column - fixed width
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const incomeTextWidth = doc.getTextWidth(totalIncomeText);
    const incomeTextX = language === "ar" ? pageWidth - incomeTextWidth - 14 : 14;
    renderText(doc, totalIncomeText, incomeTextX, currentY, {
      language,
      fontSize: 12,
      fontName: getTableFont(fontInfo)
    });
  }

  // Expense Table
  if (expenseRows.length > 0) {
    currentY += 15;
    const expenseTitle = t("expenseEvents");
    
    // Set font size first to get accurate width measurement
    doc.setFontSize(14);
    const titleWidth = doc.getTextWidth(expenseTitle);
    const titleX = language === "ar" ? pageWidth - titleWidth - 14 : 14;
    
    renderText(doc, expenseTitle, titleX, currentY, {
      language,
      fontSize: 14,
      fontName: fontInfo.fontName
    });
    currentY += 8; // Reduced spacing to keep title close to table

    autoTable(doc, {
      head: [tableColumn],
      body: expenseRows,
      startY: currentY + 3, // Reduced gap between title and table
      styles: {
        halign: language === "ar" ? "right" : "left",
        font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
        fontSize: 10,
        cellPadding: 4,
        lineColor: [200, 200, 200],
        lineWidth: 0.1,
        overflow: 'linebreak', // Handle long text properly
        cellWidth: 'wrap' // Allow cells to wrap content
      },
      headStyles: {
        font: getAutoTableFont(fontInfo, language), // Use autoTable-compatible font
        fontStyle: 'bold', // Always use bold for headers
        halign: language === "ar" ? "right" : "left",
        fillColor: [231, 76, 60], // EXACT original red
        textColor: [255, 255, 255],
        fontSize: 11,
      },
      alternateRowStyles: {
        fillColor: [248, 249, 250],
      },
      columnStyles: {
        0: { halign: language === "ar" ? "right" : "left", cellWidth: 'auto' }, // Title column - auto width
        1: { halign: language === "ar" ? "right" : "center", cellWidth: 30 }, // Date column - fixed width
        2: { halign: language === "ar" ? "right" : "right", cellWidth: 25 }, // Amount column - fixed width
        3: { halign: language === "ar" ? "right" : "center", cellWidth: 25 } // Status column - fixed width
      }
    });

    currentY = (doc as any).lastAutoTable.finalY + 10;
    const expenseTextWidth = doc.getTextWidth(totalExpensesText);
    const expenseTextX = language === "ar" ? pageWidth - expenseTextWidth - 14 : 14;
    renderText(doc, totalExpensesText, expenseTextX, currentY, {
      language,
      fontSize: 12,
      fontName: getTableFont(fontInfo)
    });
  }

  return currentY;
};
