// Main exports
export { generateCustomPDF } from './generatePDF';

// Component exports
export { default as PDFGeneratorModal } from './components/PDFGeneratorModal';

// Type exports
export type {
  PDFField,
  PDFOptions,
  PDFData,
  PDFMode,
  FontInfo,
  PDFGeneratorModalProps,
  RenderTextOptions
} from './types';

// Utility exports
export {
  convertToArabicNumerals,
  formatArabicCurrency,
  formatArabicDate,
  formatEnglishDate,
  formatDateForPDF,
  formatArabicText,
  formatMixedContent,
  containsArabic
} from './utils/formatters';

export { getTranslation } from './utils/translations';

export {
  loadLogo,
  loadFont,
  loadPerfectFont,
  renderText,
  getTableFont,
  getHeaderFontStyle,
  getFallbackFont,
  getAutoTableFont
} from './utils/fontHelpers';

export { dataContainsArabic } from './utils/dataHelpers';

// Table generator exports
export { generateEventsTable } from './utils/eventsTable';
export { generateLocationsTable } from './utils/locationsTable';
export { generateContactsTable } from './utils/contactsTable';
export { generateReservationsTable } from './utils/reservationsTable';
export { generateContractsTable } from './utils/contractsTable';

// Helper function for modal usage
export const openAdvancedPDFGenerator = (
  data: import('./types').PDFData,
  title: string,
  mode: import('./types').PDFMode,
  onModalOpen: (modalProps: any) => void
) => {
  const modalProps = {
    isOpen: true,
    data,
    title,
    mode,
    onClose: () => onModalOpen({ isOpen: false })
  };
  onModalOpen(modalProps);
};
